# 多基站接收辐射源信号仿真

## 场景描述

本仿真模拟了一个辐射源和四个接收基站的场景：

- **场景范围**：2000米 × 2000米
- **辐射源位置**：默认位置 (200, 300) 米，可在代码中修改
- **接收基站位置**：
  - 基站1：(-1000, -1000) 米 - 西南角
  - 基站2：(-1000, 1000) 米 - 西北角
  - 基站3：(1000, -1000) 米 - 东南角
  - 基站4：(1000, 1000) 米 - 东北角

## 仿真原理

仿真基于以下物理模型：

1. **自由空间路径损耗**：`Path Loss = 20*log10(4*π*d/λ)`
2. **传播时延**：`τ = d/c`
3. **相位延迟**：`φ = -2*π*fc*d/c`
4. **加性高斯白噪声**：可控信噪比

## 使用方法

### MATLAB版本

```matlab
% 运行仿真
run('multi_station_simulation.m');

% 查看生成的数据
% 每个基站的IQ数据都存储在iq_data_struct结构体中
% 例如，访问基站1的I路数据：
plot(iq_data_struct.station_1.I_data);
title('基站1 I路数据');

% 访问基站2的Q路数据：
plot(iq_data_struct.station_2.Q_data);
title('基站2 Q路数据');

% 访问基站3的复数数据：
plot(abs(iq_data_struct.station_3.complex_data));
title('基站3 信号幅度');
```

### Python版本

```python
# 运行仿真
python multi_station_simulation.py

# 查看生成的数据文件和图像
# 数据保存在multi_station_data.npz中
# 图像保存为PNG文件
```

## 生成的数据

### MATLAB变量

- `iq_data_struct`: 包含所有基站数据的结构体
  - `station_1`, `station_2`, `station_3`, `station_4`: 各基站数据
    - `I_data`: I路数据
    - `Q_data`: Q路数据
    - `complex_data`: 复数IQ数据
    - `coordinates`: 基站坐标
    - `distance`: 到辐射源的距离
    - `angle`: 到辐射源的角度
    - `path_loss_db`: 路径损耗(dB)
- `I_data`: 4×1000 矩阵，所有基站的I路数据
- `Q_data`: 4×1000 矩阵，所有基站的Q路数据
- `received_signals`: 4×1000 复数矩阵，所有基站的接收信号
- `distances`: 各基站到辐射源的距离
- `angles`: 各基站到辐射源的角度
- `station_coords`: 基站坐标
- `source_coords`: 辐射源坐标

### Python输出

- `multi_station_data.npz`: 包含所有仿真数据的NumPy压缩文件
- `scenario_layout.png`: 场景几何布局图
- `iq_signals_all_stations.png`: 所有基站的IQ信号时域波形
- `power_comparison.png`: 各基站接收功率对比
- `iq_constellations.png`: 各基站IQ星座图
- `distance_power_relationship.png`: 距离vs功率关系验证

## 修改仿真参数

您可以修改以下参数来测试不同场景：

### 辐射源位置
```matlab
% MATLAB
source_x = 200;  % 辐射源X坐标 (米)
source_y = 300;  % 辐射源Y坐标 (米)
```

```python
# Python
source_x = 200  # 辐射源X坐标 (米)
source_y = 300  # 辐射源Y坐标 (米)
```

### 信号参数
```matlab
% MATLAB
fc = 2.4e9;     % 载频 (Hz)
fs = 10e6;      % 采样频率 (Hz)
snr = 15;       % 信噪比 (dB)
n = 1000;       % 采样点数
```

```python
# Python
fc = 2.4e9      # 载频 (Hz)
fs = 10e6       # 采样频率 (Hz)
snr = 15        # 信噪比 (dB)
n = 1000        # 采样点数
```

## 数据分析示例

### 距离估计
基于接收功率可以估计辐射源到基站的距离：

```matlab
% MATLAB
% 假设已知发射功率为0dBm
for i = 1:4
    rx_power = 10*log10(mean(abs(received_signals(i,:)).^2));
    estimated_path_loss = -rx_power;  % dB
    estimated_distance = lambda * 10^(estimated_path_loss/20) / (4*pi);
    fprintf('基站%d估计距离: %.1f米 (实际: %.1f米)\n', i, estimated_distance, distances(i));
end
```

### 辐射源定位
通过多基站的距离信息可以定位辐射源：

```matlab
% MATLAB
% 简单的三角定位示例
% 需要至少3个基站的距离信息
% 这里使用最小二乘法求解
A = zeros(3, 2);
b = zeros(3, 1);
for i = 1:3
    A(i,:) = 2 * [station_coords(i,1), station_coords(i,2)];
    b(i) = distances(i)^2 - station_coords(i,1)^2 - station_coords(i,2)^2;
end
estimated_position = (A'*A)\(A'*b);
fprintf('估计辐射源位置: (%.1f, %.1f)米\n', estimated_position(1), estimated_position(2));
fprintf('实际辐射源位置: (%.1f, %.1f)米\n', source_x, source_y);
```

## 注意事项

1. 仿真使用简化的自由空间传播模型
2. 未考虑多径效应、衰落和阴影效应
3. 假设辐射源和基站之间视距传播
4. 基站接收机假设为理想接收机
5. 可以根据需要修改信号类型（当前使用复高斯信号）
