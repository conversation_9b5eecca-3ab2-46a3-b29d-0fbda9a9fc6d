# 快速开始指南

## 问题解决

您遇到的 `awgn 需要 Communications Toolbox` 错误已经解决！

## 立即开始使用

### 方法1: MATLAB简化版本（推荐）

```matlab
% 在MATLAB命令窗口中运行：
run('simple_iq_simulation.m');
```

这个版本：
- ✅ 不需要任何特殊工具箱
- ✅ 只使用基础MATLAB函数
- ✅ 生成完整的IQ数据
- ✅ 包含距离信息建模

### 方法2: 测试验证

```matlab
% 测试仿真是否正常工作：
run('test_simulation.m');
```

### 方法3: Python版本

如果您更喜欢Python：

```bash
# 安装依赖
pip install numpy matplotlib scipy

# 运行仿真
python generate_iq_data.py
```

## 生成的数据

运行成功后，您将得到：

### MATLAB变量
- `I_data`: 8×500 I路数据矩阵
- `Q_data`: 8×500 Q路数据矩阵  
- `X1`: 8×500 复数IQ数据矩阵
- `distances`: [1000, 2000, 5000] 米
- `theta`: [10, 30, 60] 度

### 可视化图表
- 图1: IQ信号时域波形
- 图2: 路径损耗vs距离关系
- 图3: 各阵元功率对比
- 图4: IQ星座图

## 核心特性

✅ **距离建模**: 自由空间路径损耗  
✅ **相位延迟**: 传播距离引起的相位变化  
✅ **多阵元**: 8元线性阵列  
✅ **多信源**: 3个不同距离的信源  
✅ **噪声添加**: 可控信噪比  

## 仿真参数

- **载频**: 2.4 GHz
- **采样频率**: 10 MHz  
- **信噪比**: 10 dB
- **阵元间距**: 0.5 波长
- **信源距离**: 1km, 2km, 5km
- **信源角度**: 10°, 30°, 60°

## 如果仍有问题

1. **确保使用 `simple_iq_simulation.m`**，不要使用 `data.m`
2. **检查MATLAB版本**，建议R2016a或更新版本
3. **查看错误信息**，运行 `test_simulation.m` 获取详细诊断

## 下一步

数据生成后，您可以：
- 实现距离估计算法
- 分析IQ数据特征
- 验证DOA估计方法
- 研究阵列信号处理

---

**重要**: 现在的代码完全不依赖Communications Toolbox或Signal Processing Toolbox！
