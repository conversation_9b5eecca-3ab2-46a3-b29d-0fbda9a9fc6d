# 距离感知IQ数据仿真

这个项目实现了基于阵列信号处理的距离感知IQ数据仿真，能够生成包含辐射源到基站距离信息的仿真数据。

## 文件说明

### MATLAB版本
- `simple_iq_simulation.m` - **推荐使用** 简化仿真脚本，不依赖特殊工具箱
- `data.m` - 完整仿真脚本（需要Communications Toolbox）
- `demo_distance_simulation.m` - 演示脚本，展示仿真结果和分析
- `test_simulation.m` - 测试脚本，验证仿真是否正常运行

### Python版本
- `distance_simulation.py` - Python版本仿真脚本
- `generate_iq_data.py` - 无GUI版本Python脚本
- `analyze_iq_data.py` - IQ数据分析脚本

### 其他文件
- `README.md` - 本说明文件
- `requirements.txt` - Python依赖包列表

## 仿真特性

### 基础参数
- **阵元数**: 8个
- **阵元间距**: 0.5波长
- **信源数**: 3个
- **载频**: 2.4 GHz
- **采样频率**: 10 MHz
- **信噪比**: 10 dB

### 距离建模
1. **自由空间路径损耗**: 基于公式 `Path Loss = 20*log10(4*pi*d/λ)`
2. **传播时延**: 考虑电磁波传播时间 `τ = d/c`
3. **相位延迟**: 由传播距离引起的相位变化
4. **多径效应**: 可选的多径传播模拟

### 输出数据
- `I_data`: I路数据矩阵 (8×500)
- `Q_data`: Q路数据矩阵 (8×500)
- `X1`: 复数IQ数据矩阵 (8×500)
- `Rxx`: 协方差矩阵 (8×8)
- `distances`: 各信源距离 [1000, 2000, 5000] 米
- `theta`: 各信源角度 [10°, 30°, 60°]

## 使用方法

### MATLAB版本（推荐）

#### 快速开始
```matlab
% 运行简化仿真（不需要特殊工具箱）
run('simple_iq_simulation.m');
```

#### 测试安装
```matlab
% 测试仿真是否正常工作
run('test_simulation.m');
```

#### 完整演示
```matlab
% 运行演示脚本
run('demo_distance_simulation.m');
```

#### 查看生成的数据
```matlab
% 查看IQ数据
figure;
plot(real(X1(1,:))); % 第1个阵元的I路数据
title('I-channel Data');

figure;
plot(imag(X1(1,:))); % 第1个阵元的Q路数据
title('Q-channel Data');
```

### Python版本
```python
# 运行仿真
python distance_simulation.py

# 或运行无GUI版本
python generate_iq_data.py

# 分析数据
python analyze_iq_data.py
```

## 距离估计方法

代码中实现了三种距离估计方法的示例：

### 1. 基于RSSI的距离估计
- 利用接收信号强度指示器
- 基于自由空间路径损耗模型
- 适用于视距传播环境

### 2. 基于TOA的距离估计
- 利用信号到达时间
- 通过相关运算检测时延
- 需要已知的参考信号

### 3. 多径传播建模
- 考虑直射径和反射径
- 模拟实际传播环境
- 提供更真实的信号特征

## 关键公式

### 自由空间路径损耗
```
L = 20*log10(4*π*d/λ) [dB]
```
其中：
- d: 传播距离 (米)
- λ: 波长 (米)
- L: 路径损耗 (dB)

### 传播时延
```
τ = d/c
```
其中：
- c: 光速 (3×10⁸ m/s)
- τ: 传播时延 (秒)

### 相位延迟
```
φ = -2*π*fc*τ = -2*π*fc*d/c
```
其中：
- fc: 载频 (Hz)
- φ: 相位延迟 (弧度)

## 仿真结果

运行仿真后会生成以下图表：
1. **IQ数据时域波形** - 显示I/Q路数据、幅度和相位
2. **路径损耗曲线** - 距离与路径损耗的关系
3. **功率分布图** - 各阵元接收功率对比
4. **IQ星座图** - 信号的复平面分布
5. **距离-功率验证** - 理论值与仿真值对比

## 应用场景

- 雷达信号处理
- 无线通信系统
- 定位算法验证
- 阵列信号处理研究
- DOA估计算法测试

## 扩展功能

可以通过修改参数实现：
- 不同的阵列几何结构
- 更多信源数量
- 不同的信道模型
- 各种噪声环境
- 复杂的多径场景

## 故障排除

### 常见问题

#### 1. "awgn 需要 Communications Toolbox" 错误
**解决方案**: 使用 `simple_iq_simulation.m` 代替 `data.m`
```matlab
run('simple_iq_simulation.m');  % 不需要特殊工具箱
```

#### 2. "xcorr 需要 Signal Processing Toolbox" 错误
**解决方案**: 代码已经修改为使用基础MATLAB函数

#### 3. Python版本图像不显示中文
**解决方案**: 使用 `generate_iq_data.py`，它使用英文标签

#### 4. 图像无法显示
**解决方案**:
- MATLAB: 确保有图形界面
- Python: 使用无GUI版本 `generate_iq_data.py`

### 推荐使用顺序
1. **MATLAB用户**: `simple_iq_simulation.m` → `test_simulation.m`
2. **Python用户**: `generate_iq_data.py` → `analyze_iq_data.py`

## 注意事项

1. 仿真假设理想的阵列响应
2. 使用简化的信道模型
3. 噪声为加性高斯白噪声
4. 适用于远场信源假设
5. **简化版本不需要任何特殊工具箱**
