#!/usr/bin/env python3
"""
IQ数据分析脚本
分析仿真生成的距离感知IQ数据
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy.signal import welch
import warnings
warnings.filterwarnings('ignore')

# 设置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_and_analyze():
    """加载并分析仿真数据"""
    print("=== IQ数据分析 ===\n")
    
    # 加载仿真数据
    data = np.load('simulation_data.npz')
    I_data = data['I_data']
    Q_data = data['Q_data']
    X1 = data['X1']
    distances = data['distances']
    theta = data['theta']
    fc = float(data['fc'])
    fs = float(data['fs'])
    lambda_wave = float(data['lambda'])
    Rxx = data['Rxx']
    
    print(f"数据加载完成:")
    print(f"IQ数据形状: {I_data.shape}")
    print(f"距离信息: {distances} 米")
    print(f"角度信息: {theta} 度")
    print(f"载频: {fc/1e9:.2f} GHz")
    print(f"采样频率: {fs/1e6:.2f} MHz")
    
    # 1. IQ星座图分析
    plt.figure(figsize=(15, 10))
    
    for i in range(min(4, len(distances))):
        plt.subplot(2, 3, i+1)
        # 取每个阵元的IQ数据样本
        samples = X1[i, :200]  # 前200个样本
        plt.scatter(np.real(samples), np.imag(samples), alpha=0.6, s=20)
        plt.title(f'Element {i+1} IQ Constellation')
        plt.xlabel('I')
        plt.ylabel('Q')
        plt.grid(True, alpha=0.3)
        plt.axis('equal')
    
    # 2. 功率谱密度分析
    plt.subplot(2, 3, 5)
    for i in range(min(3, I_data.shape[0])):
        f, psd = welch(X1[i, :], fs, nperseg=128)
        plt.semilogy(f/1e6, psd, label=f'Element {i+1}')
    plt.title('Power Spectral Density')
    plt.xlabel('Frequency (MHz)')
    plt.ylabel('PSD')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 3. Correlation matrix visualization
    plt.subplot(2, 3, 6)
    correlation_matrix = np.abs(Rxx)
    im = plt.imshow(correlation_matrix, cmap='hot', interpolation='nearest')
    plt.title('Inter-element Correlation Matrix')
    plt.xlabel('Element')
    plt.ylabel('Element')
    plt.colorbar(im)
    
    plt.tight_layout()
    plt.savefig('iq_analysis.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    # 4. 距离特征分析
    plt.figure(figsize=(12, 8))
    
    # 计算各阵元的平均功率
    plt.subplot(2, 2, 1)
    powers = np.mean(np.abs(X1)**2, axis=1)
    powers_db = 10 * np.log10(powers)
    plt.plot(range(1, len(powers)+1), powers_db, 'o-', linewidth=2, markersize=8)
    plt.title('Average Received Power per Element')
    plt.xlabel('Element Number')
    plt.ylabel('Power (dB)')
    plt.grid(True)
    
    # 理论vs实际功率对比
    plt.subplot(2, 2, 2)
    theoretical_powers = []
    measured_powers = []
    
    for i, dist in enumerate(distances):
        # 理论功率 (基于自由空间路径损耗)
        path_loss_db = 20 * np.log10(4 * np.pi * dist / lambda_wave)
        theoretical_powers.append(-path_loss_db)
        
        # 测量功率 (假设每个信源主要影响对应的阵元)
        if i < I_data.shape[0]:
            measured_power = 10 * np.log10(np.mean(np.abs(X1[i, :])**2))
            measured_powers.append(measured_power)
    
    if measured_powers:
        # 归一化到相同基准
        measured_powers = np.array(measured_powers)
        measured_powers = measured_powers - np.max(measured_powers)
        
        plt.plot(distances, theoretical_powers, 'b-o', linewidth=2, markersize=8, label='Theoretical')
        plt.plot(distances[:len(measured_powers)], measured_powers, 'r-s', linewidth=2, markersize=8, label='Simulated')
        plt.xlabel('Distance (m)')
        plt.ylabel('Relative Power (dB)')
        plt.title('Distance vs Received Power')
        plt.legend()
        plt.grid(True)
    
    # 时域信号分析
    plt.subplot(2, 2, 3)
    time_axis = np.arange(100) / fs * 1e6  # 前100个样本，转换为微秒
    plt.plot(time_axis, np.abs(X1[0, :100]), 'b-', linewidth=2, label='Magnitude')
    plt.plot(time_axis, np.real(X1[0, :100]), 'r--', alpha=0.7, label='I-channel')
    plt.plot(time_axis, np.imag(X1[0, :100]), 'g--', alpha=0.7, label='Q-channel')
    plt.title('Element 1 Time Domain Signal (First 100 samples)')
    plt.xlabel('Time (μs)')
    plt.ylabel('Amplitude')
    plt.legend()
    plt.grid(True)

    # Phase analysis
    plt.subplot(2, 2, 4)
    phases = np.angle(X1[0, :100]) * 180 / np.pi
    plt.plot(time_axis, phases, 'purple', linewidth=2)
    plt.title('Element 1 Phase Variation')
    plt.xlabel('Time (μs)')
    plt.ylabel('Phase (degrees)')
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('distance_analysis.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    # 5. 距离估计性能评估
    print(f"\n=== 距离估计性能评估 ===")
    
    # 基于功率的距离估计
    print(f"\n基于功率的距离估计:")
    for i in range(min(3, len(distances))):
        if i < I_data.shape[0]:
            received_power = np.mean(np.abs(X1[i, :])**2)
            received_power_db = 10 * np.log10(received_power)
            
            # 简化的距离估计 (假设已知发射功率)
            # 这里使用经验公式进行估计
            estimated_distance = 1000 * (10**(-received_power_db/20))
            error_percent = abs(estimated_distance - distances[i]) / distances[i] * 100
            
            print(f"信源{i+1}: 实际={distances[i]}m, 估算={estimated_distance:.0f}m, 误差={error_percent:.1f}%")
    
    # 信号质量指标
    print(f"\n信号质量指标:")
    for i in range(min(3, I_data.shape[0])):
        # 信噪比估计
        signal_power = np.mean(np.abs(X1[i, :])**2)
        # 简化的噪声功率估计 (使用信号的高频成分)
        noise_estimate = np.var(np.diff(X1[i, :]))
        snr_estimate = 10 * np.log10(signal_power / noise_estimate)
        
        # 峰值平均功率比 (PAPR)
        peak_power = np.max(np.abs(X1[i, :])**2)
        avg_power = np.mean(np.abs(X1[i, :])**2)
        papr = 10 * np.log10(peak_power / avg_power)
        
        print(f"阵元{i+1}: SNR≈{snr_estimate:.1f}dB, PAPR={papr:.1f}dB")
    
    print(f"\n分析完成！图像已保存为:")
    print(f"- iq_analysis.png: IQ数据综合分析")
    print(f"- distance_analysis.png: 距离特征分析")
    
    return {
        'I_data': I_data,
        'Q_data': Q_data,
        'X1': X1,
        'distances': distances,
        'powers_db': powers_db,
        'correlation_matrix': correlation_matrix
    }

if __name__ == "__main__":
    analysis_results = load_and_analyze()
