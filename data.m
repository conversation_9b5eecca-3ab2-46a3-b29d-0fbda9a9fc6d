% 基础参数设置
derad = pi/180;                                    % 角度→弧度
radeg = 180/pi;                                    % 弧度→角度
twpi = 2*pi;
kelm = 8;                                          % 阵元数
dd = 0.5;                                          % 阵元间距(波长为单位)
d = 0:dd:(kelm-1)*dd;                             % 阵元位置
iwave = 3;                                         % 信源数
theta = [10 30 60];                               % 波达方向(度)
snr = 10;                                         % 信噪比(dB)
n = 500;                                          % 采样数

% 距离相关参数
c = 3e8;                                          % 光速 m/s
fc = 2.4e9;                                       % 载频 2.4GHz
lambda = c/fc;                                    % 波长
fs = 10e6;                                        % 采样频率 10MHz

% 辐射源到基站的距离(米)
distances = [1000, 2000, 5000];                  % 对应三个信源的距离

% 基础信号生成
A = exp(-j*twpi*d.'*sin(theta*derad));           % 方向向量
S = randn(iwave, n);                             % 基础信源信号

% 添加距离相关的相位和幅度衰减
S_with_distance = zeros(size(S));
for i = 1:iwave
    % 传播时延
    tau = distances(i) / c;                       % 传播时延

    % 路径损耗 (自由空间传播损耗)
    path_loss_db = 20*log10(4*pi*distances(i)/lambda);
    path_loss_linear = 10^(-path_loss_db/20);

    % 相位延迟 (由于传播距离)
    phase_delay = -2*pi*fc*tau;

    % 生成带有距离信息的信号
    % 包含载频、相位延迟和路径损耗
    t = (0:n-1)/fs;                              % 时间向量
    carrier = exp(j*(2*pi*fc*t + phase_delay));  % 载波信号

    % 调制基带信号到载频
    S_with_distance(i,:) = S(i,:) .* carrier * path_loss_linear;
end

% 接收信号
X = A * S_with_distance;                         % 接收信号

% 添加噪声
X1 = awgn(X, snr, 'measured');                  % 添加高斯白噪声

% 计算协方差矩阵
Rxx = X1 * X1' / n;                              % 协方差矩阵

% IQ数据分离
I_data = real(X1);                               % I路数据
Q_data = imag(X1);                               % Q路数据

% 显示仿真结果
fprintf('仿真参数:\n');
fprintf('阵元数: %d\n', kelm);
fprintf('信源数: %d\n', iwave);
fprintf('载频: %.2f GHz\n', fc/1e9);
fprintf('采样频率: %.2f MHz\n', fs/1e6);
fprintf('信噪比: %d dB\n', snr);
fprintf('\n信源信息:\n');
for i = 1:iwave
    fprintf('信源%d: 角度=%d°, 距离=%d米\n', i, theta(i), distances(i));
end

% 绘制结果
figure(1);
subplot(2,2,1);
plot(real(X1(1,:)));
title('第1个阵元接收信号的I路数据');
xlabel('采样点');
ylabel('幅度');

subplot(2,2,2);
plot(imag(X1(1,:)));
title('第1个阵元接收信号的Q路数据');
xlabel('采样点');
ylabel('幅度');

subplot(2,2,3);
plot(abs(X1(1,:)));
title('第1个阵元接收信号的幅度');
xlabel('采样点');
ylabel('幅度');

subplot(2,2,4);
plot(angle(X1(1,:))*radeg);
title('第1个阵元接收信号的相位');
xlabel('采样点');
ylabel('相位(度)');

% 距离估计验证
figure(2);
% 计算不同距离下的路径损耗
test_distances = 500:100:6000;
path_losses = 20*log10(4*pi*test_distances/lambda);
plot(test_distances, path_losses);
title('自由空间路径损耗 vs 距离');
xlabel('距离(米)');
ylabel('路径损耗(dB)');
grid on;

% 标记仿真中使用的距离点
hold on;
for i = 1:iwave
    sim_loss = 20*log10(4*pi*distances(i)/lambda);
    plot(distances(i), sim_loss, 'ro', 'MarkerSize', 8, 'LineWidth', 2);
    text(distances(i), sim_loss+5, sprintf('信源%d', i));
end
hold off;

% 距离估计算法示例
fprintf('\n=== 距离估计验证 ===\n');

% 方法1: 基于接收信号强度(RSSI)的距离估计
fprintf('\n方法1: 基于RSSI的距离估计\n');
for i = 1:kelm
    % 计算接收功率
    received_power = mean(abs(X1(i,:)).^2);
    received_power_db = 10*log10(received_power);

    % 假设发射功率为0dBm，估算距离
    % 使用自由空间路径损耗公式反推距离
    % Path Loss = 20*log10(4*pi*d/lambda)
    % d = lambda * 10^(Path_Loss/20) / (4*pi)

    % 这里简化处理，假设第一个阵元接收到的主要是第一个信源
    if i == 1
        estimated_path_loss = -received_power_db + 10*log10(mean(abs(S(1,:)).^2)); % 估算路径损耗
        estimated_distance = lambda * 10^(estimated_path_loss/20) / (4*pi);
        fprintf('阵元%d估算距离: %.1f米 (实际距离: %d米)\n', i, estimated_distance, distances(1));
    end
end

% 方法2: 基于相位差的距离估计 (TOA - Time of Arrival)
fprintf('\n方法2: 基于时延的距离估计\n');

% 生成参考信号(已知的发射信号)
reference_signal = S(1,:);

% 对第一个阵元的接收信号进行相关运算
correlation = xcorr(X1(1,:), reference_signal);
[max_corr, max_idx] = max(abs(correlation));

% 计算时延
delay_samples = max_idx - length(reference_signal);
delay_time = delay_samples / fs;
estimated_distance_toa = delay_time * c;

fprintf('相关峰位置: %d (样本)\n', delay_samples);
fprintf('估算时延: %.2e 秒\n', delay_time);
fprintf('TOA估算距离: %.1f米 (实际距离: %d米)\n', estimated_distance_toa, distances(1));

% 方法3: 基于多径效应的距离特征
fprintf('\n方法3: 多径传播模型\n');

% 添加多径效应的仿真
multipath_delays = [0, 1e-6, 2e-6];  % 多径时延 (直射径 + 反射径)
multipath_gains = [1, 0.3, 0.1];     % 多径增益

% 为第一个信源添加多径效应
S_multipath = zeros(1, n);
for path = 1:length(multipath_delays)
    delay_samples_mp = round(multipath_delays(path) * fs);
    if delay_samples_mp < n
        S_multipath(1, (delay_samples_mp+1):end) = S_multipath(1, (delay_samples_mp+1):end) + ...
            multipath_gains(path) * S(1, 1:(n-delay_samples_mp));
    end
end

% 重新计算带多径的接收信号
X_multipath = X1;
X_multipath(1,:) = A(1,1) * S_multipath * 10^(-20*log10(4*pi*distances(1)/lambda)/20) + ...
                   sqrt(10^(-snr/10)) * (randn(1,n) + j*randn(1,n))/sqrt(2);

fprintf('多径信号已生成，包含%d条路径\n', length(multipath_delays));

% 绘制多径效果
figure(3);
subplot(2,1,1);
plot(abs(X1(1,:)));
title('无多径的接收信号幅度');
xlabel('采样点');
ylabel('幅度');

subplot(2,1,2);
plot(abs(X_multipath(1,:)));
title('含多径的接收信号幅度');
xlabel('采样点');
ylabel('幅度');

% 保存仿真数据
save_data = struct();
save_data.I_data = I_data;
save_data.Q_data = Q_data;
save_data.distances = distances;
save_data.theta = theta;
save_data.fc = fc;
save_data.fs = fs;
save_data.lambda = lambda;
save_data.Rxx = Rxx;
save_data.multipath_signal = X_multipath;

fprintf('\n=== 仿真数据已生成 ===\n');
fprintf('IQ数据矩阵大小: %dx%d (阵元数 x 采样点数)\n', size(I_data));
fprintf('数据已保存到结构体 save_data 中\n');
fprintf('包含字段: I_data, Q_data, distances, theta, fc, fs, lambda, Rxx, multipath_signal\n');