% 距离感知IQ数据仿真演示脚本
% 基于阵列信号处理的距离估计仿真

clear all; close all; clc;

fprintf('=== 距离感知IQ数据仿真演示 ===\n\n');

% 运行简化版仿真脚本 (不依赖特殊工具箱)
run('simple_iq_simulation.m');

fprintf('\n=== 仿真完成，分析结果 ===\n');

% 分析不同距离信源的信号特征
figure(4);
for i = 1:iwave
    subplot(iwave, 2, 2*i-1);
    
    % 计算该信源在各阵元的平均功率
    source_power = zeros(1, kelm);
    for k = 1:kelm
        % 简化假设：每个信源主要影响特定阵元
        source_power(k) = mean(abs(X1(k,:)).^2);
    end
    
    plot(1:kelm, 10*log10(source_power), 'o-', 'LineWidth', 2);
    title(sprintf('信源%d (距离%dm) 各阵元接收功率', i, distances(i)));
    xlabel('阵元编号');
    ylabel('功率 (dB)');
    grid on;
    
    subplot(iwave, 2, 2*i);
    
    % 显示该信源的IQ星座图
    constellation_samples = X1(i, 1:100);  % 取前100个样本
    plot(real(constellation_samples), imag(constellation_samples), '.', 'MarkerSize', 8);
    title(sprintf('信源%d IQ星座图', i));
    xlabel('I');
    ylabel('Q');
    grid on;
    axis equal;
end

% 距离-功率关系验证
figure(5);
theoretical_powers = zeros(1, iwave);
measured_powers = zeros(1, iwave);

for i = 1:iwave
    % 理论功率 (基于自由空间路径损耗)
    path_loss_db = 20*log10(4*pi*distances(i)/lambda);
    theoretical_powers(i) = -path_loss_db;
    
    % 测量功率 (第一个阵元)
    measured_powers(i) = 10*log10(mean(abs(X1(1,:)).^2));
end

plot(distances, theoretical_powers, 'b-o', 'LineWidth', 2, 'MarkerSize', 8);
hold on;
plot(distances, measured_powers - max(measured_powers), 'r-s', 'LineWidth', 2, 'MarkerSize', 8);
xlabel('距离 (米)');
ylabel('相对功率 (dB)');
title('距离 vs 接收功率关系');
legend('理论值', '仿真值', 'Location', 'best');
grid on;
hold off;

% 输出关键参数总结
fprintf('\n=== 仿真参数总结 ===\n');
fprintf('载频: %.2f GHz\n', fc/1e9);
fprintf('波长: %.3f 米\n', lambda);
fprintf('阵元间距: %.2f 波长\n', dd);
fprintf('采样频率: %.1f MHz\n', fs/1e6);
fprintf('信噪比: %d dB\n', snr);

fprintf('\n=== 信源配置 ===\n');
for i = 1:iwave
    path_loss = 20*log10(4*pi*distances(i)/lambda);
    fprintf('信源%d: 角度=%3d°, 距离=%4d米, 路径损耗=%.1fdB\n', ...
        i, theta(i), distances(i), path_loss);
end

fprintf('\n=== 数据输出说明 ===\n');
fprintf('I_data: %dx%d 矩阵，包含所有阵元的I路数据\n', size(I_data));
fprintf('Q_data: %dx%d 矩阵，包含所有阵元的Q路数据\n', size(Q_data));
fprintf('X1: %dx%d 复数矩阵，包含完整的IQ数据\n', size(X1));
fprintf('Rxx: %dx%d 协方差矩阵，用于DOA估计\n', size(Rxx));

fprintf('\n仿真完成！数据可用于距离估计算法验证。\n');
