#!/usr/bin/env python3
"""
距离感知IQ数据仿真 - Python版本
基于阵列信号处理的距离估计仿真
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy.signal import correlate
import warnings
warnings.filterwarnings('ignore')

def main():
    print("=== 距离感知IQ数据仿真 ===\n")
    
    # 基础参数设置
    derad = np.pi / 180                    # 角度→弧度
    radeg = 180 / np.pi                    # 弧度→角度
    twpi = 2 * np.pi
    kelm = 8                               # 阵元数
    dd = 0.5                               # 阵元间距(波长为单位)
    d = np.arange(0, kelm) * dd            # 阵元位置
    iwave = 3                              # 信源数
    theta = np.array([10, 30, 60])         # 波达方向(度)
    snr = 10                               # 信噪比(dB)
    n = 500                                # 采样数
    
    # 距离相关参数
    c = 3e8                                # 光速 m/s
    fc = 2.4e9                             # 载频 2.4GHz
    lambda_wave = c / fc                   # 波长
    fs = 10e6                              # 采样频率 10MHz
    
    # 辐射源到基站的距离(米)
    distances = np.array([1000, 2000, 5000])  # 对应三个信源的距离
    
    print(f"仿真参数:")
    print(f"阵元数: {kelm}")
    print(f"信源数: {iwave}")
    print(f"载频: {fc/1e9:.2f} GHz")
    print(f"采样频率: {fs/1e6:.2f} MHz")
    print(f"信噪比: {snr} dB")
    print(f"波长: {lambda_wave:.3f} 米")
    
    # 基础信号生成
    A = np.exp(-1j * twpi * np.outer(d, np.sin(theta * derad)))  # 方向向量
    S = np.random.randn(iwave, n) + 1j * np.random.randn(iwave, n)  # 基础信源信号
    
    # 添加距离相关的相位和幅度衰减
    S_with_distance = np.zeros_like(S, dtype=complex)
    
    for i in range(iwave):
        # 传播时延
        tau = distances[i] / c
        
        # 路径损耗 (自由空间传播损耗)
        path_loss_db = 20 * np.log10(4 * np.pi * distances[i] / lambda_wave)
        path_loss_linear = 10**(-path_loss_db / 20)
        
        # 相位延迟 (由于传播距离)
        phase_delay = -2 * np.pi * fc * tau
        
        # 生成带有距离信息的信号
        t = np.arange(n) / fs                           # 时间向量
        carrier = np.exp(1j * (2 * np.pi * fc * t + phase_delay))  # 载波信号
        
        # 调制基带信号到载频
        S_with_distance[i, :] = S[i, :] * carrier * path_loss_linear
    
    # 接收信号
    X = A @ S_with_distance                             # 接收信号
    
    # 添加噪声
    noise_power = 10**(-snr / 10)
    noise = np.sqrt(noise_power / 2) * (np.random.randn(kelm, n) + 1j * np.random.randn(kelm, n))
    X1 = X + noise
    
    # 计算协方差矩阵
    Rxx = X1 @ X1.conj().T / n
    
    # IQ数据分离
    I_data = np.real(X1)                               # I路数据
    Q_data = np.imag(X1)                               # Q路数据
    
    print(f"\n信源信息:")
    for i in range(iwave):
        path_loss = 20 * np.log10(4 * np.pi * distances[i] / lambda_wave)
        print(f"信源{i+1}: 角度={theta[i]:3d}°, 距离={distances[i]:4d}米, 路径损耗={path_loss:.1f}dB")
    
    # 绘制结果
    plt.figure(figsize=(12, 8))
    
    # 第1个阵元接收信号的I路数据
    plt.subplot(2, 2, 1)
    plt.plot(I_data[0, :])
    plt.title('第1个阵元接收信号的I路数据')
    plt.xlabel('采样点')
    plt.ylabel('幅度')
    plt.grid(True)
    
    # 第1个阵元接收信号的Q路数据
    plt.subplot(2, 2, 2)
    plt.plot(Q_data[0, :])
    plt.title('第1个阵元接收信号的Q路数据')
    plt.xlabel('采样点')
    plt.ylabel('幅度')
    plt.grid(True)
    
    # 第1个阵元接收信号的幅度
    plt.subplot(2, 2, 3)
    plt.plot(np.abs(X1[0, :]))
    plt.title('第1个阵元接收信号的幅度')
    plt.xlabel('采样点')
    plt.ylabel('幅度')
    plt.grid(True)
    
    # 第1个阵元接收信号的相位
    plt.subplot(2, 2, 4)
    plt.plot(np.angle(X1[0, :]) * radeg)
    plt.title('第1个阵元接收信号的相位')
    plt.xlabel('采样点')
    plt.ylabel('相位(度)')
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('iq_signals.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    # 距离估计验证
    plt.figure(figsize=(10, 6))
    
    # 计算不同距离下的路径损耗
    test_distances = np.arange(500, 6001, 100)
    path_losses = 20 * np.log10(4 * np.pi * test_distances / lambda_wave)
    plt.plot(test_distances, path_losses, 'b-', linewidth=2, label='理论路径损耗')
    
    # 标记仿真中使用的距离点
    for i in range(iwave):
        sim_loss = 20 * np.log10(4 * np.pi * distances[i] / lambda_wave)
        plt.plot(distances[i], sim_loss, 'ro', markersize=8, linewidth=2)
        plt.text(distances[i], sim_loss + 5, f'信源{i+1}', ha='center')
    
    plt.title('自由空间路径损耗 vs 距离')
    plt.xlabel('距离(米)')
    plt.ylabel('路径损耗(dB)')
    plt.grid(True)
    plt.legend()
    plt.savefig('path_loss.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    # 距离估计算法示例
    print(f"\n=== 距离估计验证 ===")
    
    # 方法1: 基于接收信号强度(RSSI)的距离估计
    print(f"\n方法1: 基于RSSI的距离估计")
    received_power = np.mean(np.abs(X1[0, :])**2)
    received_power_db = 10 * np.log10(received_power)
    
    # 假设发射功率为0dBm，估算距离
    source_power_db = 10 * np.log10(np.mean(np.abs(S[0, :])**2))
    estimated_path_loss = -received_power_db + source_power_db
    estimated_distance = lambda_wave * 10**(estimated_path_loss/20) / (4 * np.pi)
    
    print(f"阵元1估算距离: {estimated_distance:.1f}米 (实际距离: {distances[0]}米)")
    
    # 方法2: 基于相位差的距离估计 (TOA)
    print(f"\n方法2: 基于时延的距离估计")
    reference_signal = S[0, :]
    correlation = correlate(X1[0, :], reference_signal, mode='full')
    max_idx = np.argmax(np.abs(correlation))
    
    delay_samples = max_idx - len(reference_signal) + 1
    delay_time = delay_samples / fs
    estimated_distance_toa = abs(delay_time) * c
    
    print(f"相关峰位置: {delay_samples} (样本)")
    print(f"估算时延: {delay_time:.2e} 秒")
    print(f"TOA估算距离: {estimated_distance_toa:.1f}米 (实际距离: {distances[0]}米)")
    
    # 保存数据
    data_dict = {
        'I_data': I_data,
        'Q_data': Q_data,
        'X1': X1,
        'distances': distances,
        'theta': theta,
        'fc': fc,
        'fs': fs,
        'lambda': lambda_wave,
        'Rxx': Rxx
    }
    
    np.savez('simulation_data.npz', **data_dict)
    
    print(f"\n=== 仿真数据已生成 ===")
    print(f"IQ数据矩阵大小: {I_data.shape} (阵元数 x 采样点数)")
    print(f"数据已保存到 simulation_data.npz")
    print(f"图像已保存为 iq_signals.png 和 path_loss.png")
    
    return data_dict

if __name__ == "__main__":
    simulation_data = main()
