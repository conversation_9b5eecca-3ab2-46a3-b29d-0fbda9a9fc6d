#!/usr/bin/env python3
"""
距离感知IQ数据仿真 - 无GUI版本
生成包含距离信息的IQ数据并保存图像
"""

import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
from scipy.signal import correlate
import warnings
warnings.filterwarnings('ignore')

# 设置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def main():
    print("=== Distance-Aware IQ Data Simulation ===\n")
    
    # 基础参数设置
    derad = np.pi / 180                    # 角度→弧度
    radeg = 180 / np.pi                    # 弧度→角度
    twpi = 2 * np.pi
    kelm = 8                               # 阵元数
    dd = 0.5                               # 阵元间距(波长为单位)
    d = np.arange(0, kelm) * dd            # 阵元位置
    iwave = 3                              # 信源数
    theta = np.array([10, 30, 60])         # 波达方向(度)
    snr = 10                               # 信噪比(dB)
    n = 500                                # 采样数
    
    # 距离相关参数
    c = 3e8                                # 光速 m/s
    fc = 2.4e9                             # 载频 2.4GHz
    lambda_wave = c / fc                   # 波长
    fs = 10e6                              # 采样频率 10MHz
    
    # 辐射源到基站的距离(米)
    distances = np.array([1000, 2000, 5000])  # 对应三个信源的距离
    
    print(f"Simulation Parameters:")
    print(f"Number of elements: {kelm}")
    print(f"Number of sources: {iwave}")
    print(f"Carrier frequency: {fc/1e9:.2f} GHz")
    print(f"Sampling frequency: {fs/1e6:.2f} MHz")
    print(f"SNR: {snr} dB")
    print(f"Wavelength: {lambda_wave:.3f} m")
    
    # 基础信号生成
    A = np.exp(-1j * twpi * np.outer(d, np.sin(theta * derad)))  # 方向向量
    S = np.random.randn(iwave, n) + 1j * np.random.randn(iwave, n)  # 基础信源信号
    
    # 添加距离相关的相位和幅度衰减
    S_with_distance = np.zeros_like(S, dtype=complex)
    
    for i in range(iwave):
        # 传播时延
        tau = distances[i] / c
        
        # 路径损耗 (自由空间传播损耗)
        path_loss_db = 20 * np.log10(4 * np.pi * distances[i] / lambda_wave)
        path_loss_linear = 10**(-path_loss_db / 20)
        
        # 相位延迟 (由于传播距离)
        phase_delay = -2 * np.pi * fc * tau
        
        # 生成带有距离信息的信号
        t = np.arange(n) / fs                           # 时间向量
        carrier = np.exp(1j * (2 * np.pi * fc * t + phase_delay))  # 载波信号
        
        # 调制基带信号到载频
        S_with_distance[i, :] = S[i, :] * carrier * path_loss_linear
    
    # 接收信号
    X = A @ S_with_distance                             # 接收信号
    
    # 添加噪声
    noise_power = 10**(-snr / 10)
    noise = np.sqrt(noise_power / 2) * (np.random.randn(kelm, n) + 1j * np.random.randn(kelm, n))
    X1 = X + noise
    
    # 计算协方差矩阵
    Rxx = X1 @ X1.conj().T / n
    
    # IQ数据分离
    I_data = np.real(X1)                               # I路数据
    Q_data = np.imag(X1)                               # Q路数据
    
    print(f"\nSource Information:")
    for i in range(iwave):
        path_loss = 20 * np.log10(4 * np.pi * distances[i] / lambda_wave)
        print(f"Source {i+1}: Angle={theta[i]:3d}°, Distance={distances[i]:4d}m, Path Loss={path_loss:.1f}dB")
    
    # 绘制结果
    plt.figure(figsize=(12, 8))
    
    # Element 1 I-channel data
    plt.subplot(2, 2, 1)
    plt.plot(I_data[0, :])
    plt.title('Element 1 I-channel Signal')
    plt.xlabel('Sample Index')
    plt.ylabel('Amplitude')
    plt.grid(True)
    
    # Element 1 Q-channel data
    plt.subplot(2, 2, 2)
    plt.plot(Q_data[0, :])
    plt.title('Element 1 Q-channel Signal')
    plt.xlabel('Sample Index')
    plt.ylabel('Amplitude')
    plt.grid(True)
    
    # Element 1 magnitude
    plt.subplot(2, 2, 3)
    plt.plot(np.abs(X1[0, :]))
    plt.title('Element 1 Signal Magnitude')
    plt.xlabel('Sample Index')
    plt.ylabel('Amplitude')
    plt.grid(True)
    
    # Element 1 phase
    plt.subplot(2, 2, 4)
    plt.plot(np.angle(X1[0, :]) * radeg)
    plt.title('Element 1 Signal Phase')
    plt.xlabel('Sample Index')
    plt.ylabel('Phase (degrees)')
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('iq_signals_fixed.png', dpi=150, bbox_inches='tight')
    print("✓ IQ signals plot saved as 'iq_signals_fixed.png'")
    plt.close()
    
    # 距离估计验证
    plt.figure(figsize=(10, 6))
    
    # 计算不同距离下的路径损耗
    test_distances = np.arange(500, 6001, 100)
    path_losses = 20 * np.log10(4 * np.pi * test_distances / lambda_wave)
    plt.plot(test_distances, path_losses, 'b-', linewidth=2, label='Theoretical Path Loss')
    
    # 标记仿真中使用的距离点
    for i in range(iwave):
        sim_loss = 20 * np.log10(4 * np.pi * distances[i] / lambda_wave)
        plt.plot(distances[i], sim_loss, 'ro', markersize=8, linewidth=2)
        plt.text(distances[i], sim_loss + 5, f'Source{i+1}', ha='center')
    
    plt.title('Free Space Path Loss vs Distance')
    plt.xlabel('Distance (m)')
    plt.ylabel('Path Loss (dB)')
    plt.grid(True)
    plt.legend()
    plt.savefig('path_loss_fixed.png', dpi=150, bbox_inches='tight')
    print("✓ Path loss plot saved as 'path_loss_fixed.png'")
    plt.close()
    
    # 保存数据
    data_dict = {
        'I_data': I_data,
        'Q_data': Q_data,
        'X1': X1,
        'distances': distances,
        'theta': theta,
        'fc': fc,
        'fs': fs,
        'lambda': lambda_wave,
        'Rxx': Rxx
    }
    
    np.savez('simulation_data.npz', **data_dict)
    
    print(f"\n=== Simulation Complete ===")
    print(f"IQ data matrix size: {I_data.shape} (elements x samples)")
    print(f"Data saved to: simulation_data.npz")
    print(f"Plots saved as: iq_signals_fixed.png, path_loss_fixed.png")
    
    # 简单的距离估计验证
    print(f"\n=== Distance Estimation Verification ===")
    received_power = np.mean(np.abs(X1[0, :])**2)
    received_power_db = 10 * np.log10(received_power)
    print(f"Element 1 received power: {received_power_db:.2f} dB")
    
    for i in range(iwave):
        theoretical_loss = 20 * np.log10(4 * np.pi * distances[i] / lambda_wave)
        print(f"Source {i+1} theoretical path loss: {theoretical_loss:.1f} dB")
    
    return data_dict

if __name__ == "__main__":
    simulation_data = main()
