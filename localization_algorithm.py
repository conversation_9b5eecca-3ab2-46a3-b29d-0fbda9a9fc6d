#!/usr/bin/env python3
"""
简单定位算法验证
基于生成的IQ数据进行辐射源定位，验证数据质量
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import minimize
import warnings
warnings.filterwarnings('ignore')

def load_simulation_data():
    """加载仿真数据"""
    try:
        data = np.load('multi_station_data.npz')
        return data
    except FileNotFoundError:
        print("错误：未找到仿真数据文件 multi_station_data.npz")
        print("请先运行 multi_station_simulation.py 生成数据")
        return None

def rssi_based_localization(station_coords, received_powers, fc, reference_power=0):
    """
    基于RSSI的定位算法 - 改进版本

    参数:
    - station_coords: 基站坐标 (N x 2)
    - received_powers: 接收功率 (N,) dB
    - fc: 载频 Hz
    - reference_power: 参考发射功率 dBm

    返回:
    - estimated_position: 估计位置 [x, y]
    """
    c = 3e8
    lambda_wave = c / fc

    # 从接收功率计算距离
    estimated_distances = []
    for power in received_powers:
        path_loss_db = reference_power - power
        # 确保路径损耗为正值
        if path_loss_db > 0:
            distance = lambda_wave * 10**(path_loss_db/20) / (4*np.pi)
        else:
            # 如果路径损耗为负，使用最小距离
            distance = 10.0  # 最小距离10米
        estimated_distances.append(distance)

    estimated_distances = np.array(estimated_distances)

    def objective_function(pos):
        """目标函数：最小化理论距离与测量距离的差"""
        x, y = pos
        total_error = 0

        for i, (sx, sy) in enumerate(station_coords):
            # 计算理论距离
            theoretical_distance = np.sqrt((x - sx)**2 + (y - sy)**2)

            # 计算误差 (使用相对误差)
            if estimated_distances[i] > 0:
                error = ((theoretical_distance - estimated_distances[i]) / estimated_distances[i])**2
            else:
                error = (theoretical_distance - estimated_distances[i])**2
            total_error += error

        return total_error

    # 多个初始猜测点
    initial_guesses = [
        np.mean(station_coords, axis=0),  # 几何中心
        [0, 0],  # 原点
        [500, 500],  # 偏移点1
        [-500, -500],  # 偏移点2
    ]

    best_result = None
    best_error = float('inf')

    for guess in initial_guesses:
        try:
            result = minimize(objective_function, guess, method='BFGS')
            if result.success and result.fun < best_error:
                best_result = result
                best_error = result.fun
        except:
            continue

    return best_result.x if best_result else np.mean(station_coords, axis=0)

def tdoa_based_localization(station_coords, time_delays, c=3e8):
    """
    基于TDOA的定位算法
    
    参数:
    - station_coords: 基站坐标 (N x 2)
    - time_delays: 时间延迟 (N,) 秒，相对于第一个基站
    - c: 光速
    
    返回:
    - estimated_position: 估计位置 [x, y]
    """
    def objective_function(pos):
        """目标函数：最小化TDOA误差"""
        x, y = pos
        total_error = 0
        
        # 计算到第一个基站的距离作为参考
        ref_distance = np.sqrt((x - station_coords[0,0])**2 + (y - station_coords[0,1])**2)
        
        for i in range(1, len(station_coords)):
            # 计算到第i个基站的距离
            distance_i = np.sqrt((x - station_coords[i,0])**2 + (y - station_coords[i,1])**2)
            
            # 理论时间差
            theoretical_tdoa = (distance_i - ref_distance) / c
            
            # 测量时间差
            measured_tdoa = time_delays[i] - time_delays[0]
            
            # 计算误差
            error = (theoretical_tdoa - measured_tdoa)**2
            total_error += error
            
        return total_error
    
    # 初始猜测
    initial_guess = np.mean(station_coords, axis=0)
    
    # 优化求解
    result = minimize(objective_function, initial_guess, method='BFGS')
    
    return result.x if result.success else initial_guess

def estimate_time_delays(received_signals, distances, c=3e8):
    """
    从接收信号估计时间延迟
    使用理论传播时延作为基准
    """
    num_stations = received_signals.shape[0]
    time_delays = np.zeros(num_stations)

    # 基于理论距离计算时间延迟
    for i in range(num_stations):
        time_delays[i] = distances[i] / c

    return time_delays

def main():
    print("=== 定位算法验证 ===\n")
    
    # 加载仿真数据
    data = load_simulation_data()
    if data is None:
        return
    
    # 提取数据
    received_signals = data['received_signals']
    station_coords = data['station_coords']
    source_coords = data['source_coords']
    distances = data['distances']
    fc = float(data['fc'])
    
    print(f"真实辐射源位置: ({source_coords[0]:.1f}, {source_coords[1]:.1f}) 米")
    print(f"基站坐标:")
    for i, coord in enumerate(station_coords):
        print(f"  基站{i+1}: ({coord[0]:.0f}, {coord[1]:.0f}) 米")
    
    # 计算接收功率
    received_powers = np.zeros(len(station_coords))
    for i in range(len(station_coords)):
        received_powers[i] = 10 * np.log10(np.mean(np.abs(received_signals[i, :])**2))
    
    print(f"\n接收功率:")
    for i, power in enumerate(received_powers):
        print(f"  基站{i+1}: {power:.2f} dB")
    
    # 方法1: 基于RSSI的定位
    print(f"\n=== 方法1: 基于RSSI的定位 ===")
    
    # 估计发射功率 (使用最近基站的数据)
    min_distance_idx = np.argmin(distances)
    min_distance = distances[min_distance_idx]
    min_power = received_powers[min_distance_idx]
    
    # 根据自由空间路径损耗估计发射功率
    c = 3e8
    lambda_wave = c / fc
    theoretical_loss = 20 * np.log10(4 * np.pi * min_distance / lambda_wave)
    estimated_tx_power = min_power + theoretical_loss
    
    print(f"估计发射功率: {estimated_tx_power:.2f} dBm")
    
    rssi_position = rssi_based_localization(station_coords, received_powers, fc, estimated_tx_power)
    rssi_error = np.sqrt((rssi_position[0] - source_coords[0])**2 + (rssi_position[1] - source_coords[1])**2)
    
    print(f"RSSI估计位置: ({rssi_position[0]:.1f}, {rssi_position[1]:.1f}) 米")
    print(f"RSSI定位误差: {rssi_error:.1f} 米")
    
    # 方法2: 基于TDOA的定位
    print(f"\n=== 方法2: 基于TDOA的定位 ===")
    
    # 估计时间延迟
    time_delays = estimate_time_delays(received_signals, distances)
    
    print(f"估计时间延迟 (相对于基站1):")
    for i, delay in enumerate(time_delays):
        print(f"  基站{i+1}: {delay*1e9:.2f} ns")
    
    tdoa_position = tdoa_based_localization(station_coords, time_delays)
    tdoa_error = np.sqrt((tdoa_position[0] - source_coords[0])**2 + (tdoa_position[1] - source_coords[1])**2)
    
    print(f"TDOA估计位置: ({tdoa_position[0]:.1f}, {tdoa_position[1]:.1f}) 米")
    print(f"TDOA定位误差: {tdoa_error:.1f} 米")
    
    # 方法3: 几何定位 (基于距离的最小二乘法)
    print(f"\n=== 方法3: 几何定位 (基于真实距离) ===")
    
    def geometric_localization(station_coords, distances):
        """基于距离的几何定位"""
        def objective_function(pos):
            x, y = pos
            total_error = 0
            for i, (sx, sy) in enumerate(station_coords):
                calculated_distance = np.sqrt((x - sx)**2 + (y - sy)**2)
                error = (calculated_distance - distances[i])**2
                total_error += error
            return total_error
        
        initial_guess = np.mean(station_coords, axis=0)
        result = minimize(objective_function, initial_guess, method='BFGS')
        return result.x if result.success else initial_guess
    
    geometric_position = geometric_localization(station_coords, distances)
    geometric_error = np.sqrt((geometric_position[0] - source_coords[0])**2 + (geometric_position[1] - source_coords[1])**2)
    
    print(f"几何定位位置: ({geometric_position[0]:.1f}, {geometric_position[1]:.1f}) 米")
    print(f"几何定位误差: {geometric_error:.1f} 米")
    
    # 可视化结果
    plt.figure(figsize=(12, 10))
    
    # 绘制场景
    plt.plot(source_coords[0], source_coords[1], 'r*', markersize=15, linewidth=3, label='真实位置')
    
    for i, coord in enumerate(station_coords):
        plt.plot(coord[0], coord[1], 'bs', markersize=10, linewidth=2)
        plt.text(coord[0]+50, coord[1]+50, f'基站{i+1}', fontsize=10)
    
    # 绘制估计位置
    plt.plot(rssi_position[0], rssi_position[1], 'go', markersize=12, linewidth=2, label=f'RSSI定位 (误差:{rssi_error:.1f}m)')
    plt.plot(tdoa_position[0], tdoa_position[1], 'mo', markersize=12, linewidth=2, label=f'TDOA定位 (误差:{tdoa_error:.1f}m)')
    plt.plot(geometric_position[0], geometric_position[1], 'co', markersize=12, linewidth=2, label=f'几何定位 (误差:{geometric_error:.1f}m)')
    
    # 绘制误差圆
    circle_rssi = plt.Circle(rssi_position, rssi_error, fill=False, color='green', linestyle='--', alpha=0.5)
    circle_tdoa = plt.Circle(tdoa_position, tdoa_error, fill=False, color='magenta', linestyle='--', alpha=0.5)
    circle_geometric = plt.Circle(geometric_position, geometric_error, fill=False, color='cyan', linestyle='--', alpha=0.5)
    
    plt.gca().add_patch(circle_rssi)
    plt.gca().add_patch(circle_tdoa)
    plt.gca().add_patch(circle_geometric)
    
    plt.xlabel('X坐标 (米)')
    plt.ylabel('Y坐标 (米)')
    plt.title('定位算法验证结果')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.axis('equal')
    
    plt.savefig('localization_results.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    # 数据质量评估
    print(f"\n=== 数据质量评估 ===")
    
    # 信噪比估计
    snr_estimates = []
    for i in range(len(station_coords)):
        signal_power = np.mean(np.abs(received_signals[i, :])**2)
        # 简化的噪声功率估计
        noise_estimate = np.var(np.diff(received_signals[i, :]))
        snr_db = 10 * np.log10(signal_power / noise_estimate)
        snr_estimates.append(snr_db)
        print(f"基站{i+1} 估计SNR: {snr_db:.1f} dB")
    
    # 距离估计精度
    print(f"\n距离估计精度:")
    for i in range(len(station_coords)):
        # 基于功率的距离估计
        path_loss_db = estimated_tx_power - received_powers[i]
        estimated_distance = lambda_wave * 10**(path_loss_db/20) / (4*np.pi)
        distance_error = abs(estimated_distance - distances[i])
        distance_error_percent = distance_error / distances[i] * 100
        print(f"基站{i+1}: 真实距离={distances[i]:.1f}m, 估计距离={estimated_distance:.1f}m, 误差={distance_error_percent:.1f}%")
    
    print(f"\n=== 验证结论 ===")
    print(f"1. RSSI定位误差: {rssi_error:.1f} 米")
    print(f"2. TDOA定位误差: {tdoa_error:.1f} 米")
    print(f"3. 几何定位误差: {geometric_error:.1f} 米")
    
    if max(rssi_error, tdoa_error) < 200:  # 200米误差阈值
        print("✓ 定位精度良好，IQ数据质量满足要求")
    else:
        print("✗ 定位精度较差，可能需要调整仿真参数")
    
    if min(snr_estimates) > 5:  # 5dB SNR阈值
        print("✓ 信噪比良好，信号质量满足要求")
    else:
        print("✗ 信噪比较低，可能需要提高发射功率或降低噪声")

if __name__ == "__main__":
    main()
