% 多基站接收辐射源信号仿真
% 场景：1个辐射源，4个接收基站
% 基站坐标：(-1000,-1000), (-1000,1000), (1000,-1000), (1000,1000) 米

clear all; close all; clc;

fprintf('=== 多基站辐射源信号仿真 ===\n\n');

% 基础参数设置
c = 3e8;                                          % 光速 m/s
fc = 2.4e9;                                       % 载频 2.4GHz
lambda = c/fc;                                    % 波长
fs = 10e6;                                        % 采样频率 10MHz
snr = 15;                                         % 信噪比(dB)
n = 1000;                                         % 采样数
derad = pi/180;                                   % 角度→弧度

% 基站坐标 (米)
station_coords = [
    -1000, -1000;  % 基站1：西南角
    -1000,  1000;  % 基站2：西北角
     1000, -1000;  % 基站3：东南角
     1000,  1000   % 基站4：东北角
];
num_stations = size(station_coords, 1);

% 辐射源位置 (可以修改这个位置来测试不同场景)
source_x = 200;                                   % 辐射源X坐标 (米)
source_y = 300;                                   % 辐射源Y坐标 (米)
source_coords = [source_x, source_y];

fprintf('仿真场景设置:\n');
fprintf('载频: %.2f GHz\n', fc/1e9);
fprintf('采样频率: %.2f MHz\n', fs/1e6);
fprintf('信噪比: %d dB\n', snr);
fprintf('采样点数: %d\n', n);
fprintf('波长: %.3f 米\n', lambda);
fprintf('\n辐射源位置: (%.0f, %.0f) 米\n', source_x, source_y);

fprintf('\n基站位置:\n');
for i = 1:num_stations
    fprintf('基站%d: (%.0f, %.0f) 米\n', i, station_coords(i,1), station_coords(i,2));
end

% 计算每个基站到辐射源的距离和角度
distances = zeros(num_stations, 1);
angles = zeros(num_stations, 1);
path_losses_db = zeros(num_stations, 1);

fprintf('\n距离和路径损耗计算:\n');
for i = 1:num_stations
    % 计算距离
    dx = station_coords(i,1) - source_x;
    dy = station_coords(i,2) - source_y;
    distances(i) = sqrt(dx^2 + dy^2);
    
    % 计算角度 (从辐射源到基站的方向)
    angles(i) = atan2(dy, dx) * 180/pi;
    
    % 计算自由空间路径损耗
    path_losses_db(i) = 20*log10(4*pi*distances(i)/lambda);
    
    fprintf('基站%d: 距离=%.1fm, 角度=%.1f°, 路径损耗=%.1fdB\n', ...
        i, distances(i), angles(i), path_losses_db(i));
end

% 生成辐射源信号
t = (0:n-1)/fs;                                   % 时间向量
% 生成基带信号 (可以是任意调制信号)
baseband_signal = randn(1,n) + 1j*randn(1,n);    % 复高斯信号
% 也可以使用其他信号类型，例如：
% baseband_signal = exp(1j*2*pi*1e3*t);          % 单频信号
% baseband_signal = sign(randn(1,n)) + 1j*sign(randn(1,n)); % QPSK信号

% 为每个基站生成接收信号
received_signals = zeros(num_stations, n);        % 存储所有基站的接收信号
I_data = zeros(num_stations, n);                  % I路数据
Q_data = zeros(num_stations, n);                  % Q路数据

fprintf('\n生成各基站接收信号:\n');
for i = 1:num_stations
    % 传播时延
    tau = distances(i) / c;
    
    % 路径损耗 (线性值)
    path_loss_linear = 10^(-path_losses_db(i)/20);
    
    % 相位延迟 (由于传播距离)
    phase_delay = -2*pi*fc*tau;
    
    % 载波信号 (包含相位延迟)
    carrier = exp(1j*(2*pi*fc*t + phase_delay));
    
    % 调制到载频并应用路径损耗
    modulated_signal = baseband_signal .* carrier * path_loss_linear;
    
    % 添加高斯白噪声
    signal_power = mean(abs(modulated_signal).^2);
    noise_power = signal_power / (10^(snr/10));
    noise = sqrt(noise_power/2) * (randn(1,n) + 1j*randn(1,n));
    
    % 最终接收信号
    received_signals(i,:) = modulated_signal + noise;
    
    % 分离I/Q数据
    I_data(i,:) = real(received_signals(i,:));
    Q_data(i,:) = imag(received_signals(i,:));
    
    % 计算接收功率
    rx_power_db = 10*log10(mean(abs(received_signals(i,:)).^2));
    fprintf('基站%d接收功率: %.2f dB\n', i, rx_power_db);
end

% 创建数据结构，标识每段IQ信号对应的基站
iq_data_struct = struct();
for i = 1:num_stations
    station_name = sprintf('station_%d', i);
    iq_data_struct.(station_name) = struct();
    iq_data_struct.(station_name).I_data = I_data(i,:);
    iq_data_struct.(station_name).Q_data = Q_data(i,:);
    iq_data_struct.(station_name).complex_data = received_signals(i,:);
    iq_data_struct.(station_name).coordinates = station_coords(i,:);
    iq_data_struct.(station_name).distance = distances(i);
    iq_data_struct.(station_name).angle = angles(i);
    iq_data_struct.(station_name).path_loss_db = path_losses_db(i);
end

% 可视化结果
% 1. 场景几何图
figure(1);
plot(source_x, source_y, 'r*', 'MarkerSize', 15, 'LineWidth', 3);
hold on;
for i = 1:num_stations
    plot(station_coords(i,1), station_coords(i,2), 'bs', 'MarkerSize', 10, 'LineWidth', 2);
    text(station_coords(i,1)+50, station_coords(i,2)+50, sprintf('Station%d', i), 'FontSize', 10);
    % 绘制连线
    plot([source_x, station_coords(i,1)], [source_y, station_coords(i,2)], 'k--', 'Alpha', 0.5);
end
text(source_x+50, source_y+50, 'Source', 'FontSize', 12, 'Color', 'red');
xlabel('X坐标 (米)');
ylabel('Y坐标 (米)');
title('多基站接收场景几何布局');
grid on;
axis equal;
legend('辐射源', '接收基站', 'Location', 'best');
hold off;

% 2. 各基站IQ信号时域波形
figure(2);
for i = 1:num_stations
    subplot(2,2,i);
    plot(t(1:200)*1e6, I_data(i,1:200), 'b-', 'LineWidth', 1);
    hold on;
    plot(t(1:200)*1e6, Q_data(i,1:200), 'r-', 'LineWidth', 1);
    title(sprintf('基站%d IQ信号 (前200样本)', i));
    xlabel('时间 (μs)');
    ylabel('幅度');
    legend('I路', 'Q路');
    grid on;
    hold off;
end

% 3. 各基站接收功率对比
figure(3);
rx_powers = zeros(num_stations, 1);
for i = 1:num_stations
    rx_powers(i) = 10*log10(mean(abs(received_signals(i,:)).^2));
end
bar(1:num_stations, rx_powers);
xlabel('基站编号');
ylabel('接收功率 (dB)');
title('各基站接收功率对比');
grid on;
for i = 1:num_stations
    text(i, rx_powers(i)+1, sprintf('%.1fdB', rx_powers(i)), 'HorizontalAlignment', 'center');
end

% 4. IQ星座图
figure(4);
for i = 1:num_stations
    subplot(2,2,i);
    samples = received_signals(i, 1:200);  % 前200个样本
    plot(real(samples), imag(samples), '.', 'MarkerSize', 4);
    title(sprintf('基站%d IQ星座图', i));
    xlabel('I');
    ylabel('Q');
    grid on;
    axis equal;
end

% 5. 距离vs功率关系验证
figure(5);
theoretical_powers = -path_losses_db;  % 理论功率 (负的路径损耗)
measured_powers = rx_powers - max(rx_powers);  % 归一化的测量功率
plot(distances, theoretical_powers, 'bo-', 'LineWidth', 2, 'MarkerSize', 8);
hold on;
plot(distances, measured_powers, 'rs-', 'LineWidth', 2, 'MarkerSize', 8);
xlabel('距离 (米)');
ylabel('相对功率 (dB)');
title('距离vs接收功率关系验证');
legend('理论值', '仿真值', 'Location', 'best');
grid on;
hold off;

% 保存仿真数据
fprintf('\n=== 数据保存 ===\n');
fprintf('保存的数据结构:\n');
fprintf('- iq_data_struct: 包含所有基站的IQ数据和信息\n');
fprintf('- I_data: %dx%d 矩阵 (基站数 x 采样点数)\n', size(I_data));
fprintf('- Q_data: %dx%d 矩阵 (基站数 x 采样点数)\n', size(Q_data));
fprintf('- received_signals: %dx%d 复数矩阵\n', size(received_signals));
fprintf('- distances: 各基站到辐射源的距离\n');
fprintf('- station_coords: 基站坐标\n');
fprintf('- source_coords: 辐射源坐标\n');

% 显示如何访问特定基站的数据
fprintf('\n=== 数据访问示例 ===\n');
fprintf('访问基站1的I路数据: iq_data_struct.station_1.I_data\n');
fprintf('访问基站2的Q路数据: iq_data_struct.station_2.Q_data\n');
fprintf('访问基站3的复数数据: iq_data_struct.station_3.complex_data\n');
fprintf('访问基站4的坐标: iq_data_struct.station_4.coordinates\n');

fprintf('\n仿真完成！\n');
