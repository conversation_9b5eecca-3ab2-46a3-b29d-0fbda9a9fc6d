#!/usr/bin/env python3
"""
多基站接收辐射源信号仿真 - Python版本
场景：1个辐射源，4个接收基站
基站坐标：(-1000,-1000), (-1000,1000), (1000,-1000), (1000,1000) 米
"""

import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# 设置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def main():
    print("=== Multi-Station Source Signal Simulation ===\n")
    
    # 基础参数设置
    c = 3e8                                          # 光速 m/s
    fc = 2.4e9                                       # 载频 2.4GHz
    lambda_wave = c / fc                             # 波长
    fs = 10e6                                        # 采样频率 10MHz
    snr = 15                                         # 信噪比(dB)
    n = 1000                                         # 采样数
    
    # 基站坐标 (米)
    station_coords = np.array([
        [-1000, -1000],  # 基站1：西南角
        [-1000,  1000],  # 基站2：西北角
        [ 1000, -1000],  # 基站3：东南角
        [ 1000,  1000]   # 基站4：东北角
    ])
    num_stations = station_coords.shape[0]
    
    # 辐射源位置 (可以修改这个位置来测试不同场景)
    source_x = 200                                   # 辐射源X坐标 (米)
    source_y = 300                                   # 辐射源Y坐标 (米)
    source_coords = np.array([source_x, source_y])
    
    print(f"Simulation Setup:")
    print(f"Carrier frequency: {fc/1e9:.2f} GHz")
    print(f"Sampling frequency: {fs/1e6:.2f} MHz")
    print(f"SNR: {snr} dB")
    print(f"Number of samples: {n}")
    print(f"Wavelength: {lambda_wave:.3f} m")
    print(f"\nSource position: ({source_x:.0f}, {source_y:.0f}) m")
    
    print(f"\nStation positions:")
    for i in range(num_stations):
        print(f"Station {i+1}: ({station_coords[i,0]:.0f}, {station_coords[i,1]:.0f}) m")
    
    # 计算每个基站到辐射源的距离和角度
    distances = np.zeros(num_stations)
    angles = np.zeros(num_stations)
    path_losses_db = np.zeros(num_stations)
    
    print(f"\nDistance and path loss calculations:")
    for i in range(num_stations):
        # 计算距离
        dx = station_coords[i,0] - source_x
        dy = station_coords[i,1] - source_y
        distances[i] = np.sqrt(dx**2 + dy**2)
        
        # 计算角度 (从辐射源到基站的方向)
        angles[i] = np.arctan2(dy, dx) * 180/np.pi
        
        # 计算自由空间路径损耗
        path_losses_db[i] = 20*np.log10(4*np.pi*distances[i]/lambda_wave)
        
        print(f"Station {i+1}: distance={distances[i]:.1f}m, angle={angles[i]:.1f}°, path_loss={path_losses_db[i]:.1f}dB")
    
    # 生成辐射源信号
    t = np.arange(n) / fs                            # 时间向量
    # 生成基带信号 (可以是任意调制信号)
    baseband_signal = np.random.randn(n) + 1j*np.random.randn(n)  # 复高斯信号
    
    # 为每个基站生成接收信号
    received_signals = np.zeros((num_stations, n), dtype=complex)  # 存储所有基站的接收信号
    I_data = np.zeros((num_stations, n))             # I路数据
    Q_data = np.zeros((num_stations, n))             # Q路数据
    
    print(f"\nGenerating received signals for each station:")
    for i in range(num_stations):
        # 传播时延
        tau = distances[i] / c
        
        # 路径损耗 (线性值)
        path_loss_linear = 10**(-path_losses_db[i]/20)
        
        # 相位延迟 (由于传播距离)
        phase_delay = -2*np.pi*fc*tau
        
        # 载波信号 (包含相位延迟)
        carrier = np.exp(1j*(2*np.pi*fc*t + phase_delay))
        
        # 调制到载频并应用路径损耗
        modulated_signal = baseband_signal * carrier * path_loss_linear
        
        # 添加高斯白噪声
        signal_power = np.mean(np.abs(modulated_signal)**2)
        noise_power = signal_power / (10**(snr/10))
        noise = np.sqrt(noise_power/2) * (np.random.randn(n) + 1j*np.random.randn(n))
        
        # 最终接收信号
        received_signals[i,:] = modulated_signal + noise
        
        # 分离I/Q数据
        I_data[i,:] = np.real(received_signals[i,:])
        Q_data[i,:] = np.imag(received_signals[i,:])
        
        # 计算接收功率
        rx_power_db = 10*np.log10(np.mean(np.abs(received_signals[i,:])**2))
        print(f"Station {i+1} received power: {rx_power_db:.2f} dB")
    
    # 创建数据字典，标识每段IQ信号对应的基站
    iq_data_dict = {}
    for i in range(num_stations):
        station_name = f'station_{i+1}'
        iq_data_dict[station_name] = {
            'I_data': I_data[i,:],
            'Q_data': Q_data[i,:],
            'complex_data': received_signals[i,:],
            'coordinates': station_coords[i,:],
            'distance': distances[i],
            'angle': angles[i],
            'path_loss_db': path_losses_db[i]
        }
    
    # 可视化结果
    # 1. 场景几何图
    plt.figure(figsize=(10, 8))
    plt.plot(source_x, source_y, 'r*', markersize=15, linewidth=3, label='Source')
    for i in range(num_stations):
        plt.plot(station_coords[i,0], station_coords[i,1], 'bs', markersize=10, linewidth=2)
        plt.text(station_coords[i,0]+50, station_coords[i,1]+50, f'Station{i+1}', fontsize=10)
        # 绘制连线
        plt.plot([source_x, station_coords[i,0]], [source_y, station_coords[i,1]], 'k--', alpha=0.5)
    
    plt.text(source_x+50, source_y+50, 'Source', fontsize=12, color='red')
    plt.xlabel('X Coordinate (m)')
    plt.ylabel('Y Coordinate (m)')
    plt.title('Multi-Station Reception Scenario Layout')
    plt.grid(True)
    plt.axis('equal')
    plt.legend()
    plt.savefig('scenario_layout.png', dpi=150, bbox_inches='tight')
    plt.close()
    
    # 2. 各基站IQ信号时域波形
    plt.figure(figsize=(12, 8))
    for i in range(num_stations):
        plt.subplot(2,2,i+1)
        plt.plot(t[:200]*1e6, I_data[i,:200], 'b-', linewidth=1, label='I-channel')
        plt.plot(t[:200]*1e6, Q_data[i,:200], 'r-', linewidth=1, label='Q-channel')
        plt.title(f'Station {i+1} IQ Signals (First 200 samples)')
        plt.xlabel('Time (μs)')
        plt.ylabel('Amplitude')
        plt.legend()
        plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('iq_signals_all_stations.png', dpi=150, bbox_inches='tight')
    plt.close()
    
    # 3. 各基站接收功率对比
    plt.figure(figsize=(10, 6))
    rx_powers = np.zeros(num_stations)
    for i in range(num_stations):
        rx_powers[i] = 10*np.log10(np.mean(np.abs(received_signals[i,:])**2))
    
    bars = plt.bar(range(1, num_stations+1), rx_powers)
    plt.xlabel('Station Number')
    plt.ylabel('Received Power (dB)')
    plt.title('Received Power Comparison Across Stations')
    plt.grid(True, alpha=0.3)
    for i, bar in enumerate(bars):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1, 
                f'{rx_powers[i]:.1f}dB', ha='center')
    
    plt.savefig('power_comparison.png', dpi=150, bbox_inches='tight')
    plt.close()
    
    # 4. IQ星座图
    plt.figure(figsize=(12, 8))
    for i in range(num_stations):
        plt.subplot(2,2,i+1)
        samples = received_signals[i, :200]  # 前200个样本
        plt.plot(np.real(samples), np.imag(samples), '.', markersize=4)
        plt.title(f'Station {i+1} IQ Constellation')
        plt.xlabel('I')
        plt.ylabel('Q')
        plt.grid(True)
        plt.axis('equal')
    
    plt.tight_layout()
    plt.savefig('iq_constellations.png', dpi=150, bbox_inches='tight')
    plt.close()
    
    # 5. 距离vs功率关系验证
    plt.figure(figsize=(10, 6))
    theoretical_powers = -path_losses_db  # 理论功率 (负的路径损耗)
    measured_powers = rx_powers - np.max(rx_powers)  # 归一化的测量功率
    plt.plot(distances, theoretical_powers, 'bo-', linewidth=2, markersize=8, label='Theoretical')
    plt.plot(distances, measured_powers, 'rs-', linewidth=2, markersize=8, label='Simulated')
    plt.xlabel('Distance (m)')
    plt.ylabel('Relative Power (dB)')
    plt.title('Distance vs Received Power Relationship')
    plt.legend()
    plt.grid(True)
    plt.savefig('distance_power_relationship.png', dpi=150, bbox_inches='tight')
    plt.close()
    
    # 保存数据
    np.savez('multi_station_data.npz', 
             I_data=I_data,
             Q_data=Q_data,
             received_signals=received_signals,
             distances=distances,
             angles=angles,
             station_coords=station_coords,
             source_coords=source_coords,
             path_losses_db=path_losses_db,
             fc=fc,
             fs=fs,
             lambda_wave=lambda_wave)
    
    print(f"\n=== Data Saved ===")
    print(f"Data structure saved:")
    print(f"- I_data: {I_data.shape} matrix (stations x samples)")
    print(f"- Q_data: {Q_data.shape} matrix (stations x samples)")
    print(f"- received_signals: {received_signals.shape} complex matrix")
    print(f"- distances: distances from source to each station")
    print(f"- station_coords: station coordinates")
    print(f"- source_coords: source coordinates")
    
    print(f"\n=== Data Access Examples ===")
    print(f"Access station 1 I-data: iq_data_dict['station_1']['I_data']")
    print(f"Access station 2 Q-data: iq_data_dict['station_2']['Q_data']")
    print(f"Access station 3 complex data: iq_data_dict['station_3']['complex_data']")
    print(f"Access station 4 coordinates: iq_data_dict['station_4']['coordinates']")
    
    print(f"\nGenerated images:")
    print(f"- scenario_layout.png: Geometric layout")
    print(f"- iq_signals_all_stations.png: IQ signals for all stations")
    print(f"- power_comparison.png: Power comparison")
    print(f"- iq_constellations.png: IQ constellations")
    print(f"- distance_power_relationship.png: Distance vs power")
    
    print(f"\nSimulation complete!")
    
    return iq_data_dict, I_data, Q_data, received_signals

if __name__ == "__main__":
    iq_data_dict, I_data, Q_data, received_signals = main()
