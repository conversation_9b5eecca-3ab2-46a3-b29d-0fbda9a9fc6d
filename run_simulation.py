#!/usr/bin/env python3
"""
一键运行距离感知IQ数据仿真
"""

import subprocess
import sys
import os

def main():
    print("=" * 50)
    print("距离感知IQ数据仿真系统")
    print("=" * 50)
    print()
    
    print("这个仿真系统包含以下功能:")
    print("1. 生成包含距离信息的IQ数据")
    print("2. 模拟自由空间路径损耗")
    print("3. 实现多种距离估计算法")
    print("4. 提供详细的数据分析")
    print()
    
    print("仿真参数:")
    print("- 8元线性阵列，阵元间距0.5波长")
    print("- 3个信源，距离: 1000m, 2000m, 5000m")
    print("- 载频: 2.4GHz，采样频率: 10MHz")
    print("- 信噪比: 10dB")
    print()
    
    choice = input("请选择操作:\n1. 运行完整仿真\n2. 仅生成IQ数据\n3. 仅分析现有数据\n4. 查看生成的图像\n请输入选择 (1-4): ")
    
    if choice == '1':
        print("\n正在运行完整仿真...")
        print("-" * 30)
        
        # 运行仿真
        print("步骤1: 生成IQ数据...")
        result1 = subprocess.run([sys.executable, 'distance_simulation.py'], 
                                capture_output=True, text=True)
        if result1.returncode == 0:
            print("✓ IQ数据生成完成")
        else:
            print("✗ IQ数据生成失败")
            print(result1.stderr)
            return
        
        # 运行分析
        print("步骤2: 分析IQ数据...")
        result2 = subprocess.run([sys.executable, 'analyze_iq_data.py'], 
                                capture_output=True, text=True)
        if result2.returncode == 0:
            print("✓ 数据分析完成")
        else:
            print("✗ 数据分析失败")
            print(result2.stderr)
            return
        
        print("\n仿真完成！生成的文件:")
        files = [
            'simulation_data.npz - 仿真数据',
            'iq_signals.png - IQ信号波形',
            'path_loss.png - 路径损耗曲线',
            'iq_analysis.png - IQ数据分析',
            'distance_analysis.png - 距离特征分析'
        ]
        for f in files:
            if os.path.exists(f.split(' - ')[0]):
                print(f"✓ {f}")
            else:
                print(f"✗ {f}")
    
    elif choice == '2':
        print("\n正在生成IQ数据...")
        result = subprocess.run([sys.executable, 'distance_simulation.py'])
        if result.returncode == 0:
            print("✓ IQ数据生成完成")
        else:
            print("✗ IQ数据生成失败")
    
    elif choice == '3':
        if os.path.exists('simulation_data.npz'):
            print("\n正在分析现有数据...")
            result = subprocess.run([sys.executable, 'analyze_iq_data.py'])
            if result.returncode == 0:
                print("✓ 数据分析完成")
            else:
                print("✗ 数据分析失败")
        else:
            print("✗ 未找到仿真数据文件，请先运行仿真")
    
    elif choice == '4':
        print("\n检查生成的图像文件:")
        image_files = ['iq_signals.png', 'path_loss.png', 'iq_analysis.png', 'distance_analysis.png']
        for img in image_files:
            if os.path.exists(img):
                print(f"✓ {img} - 已生成")
            else:
                print(f"✗ {img} - 未找到")
        
        if any(os.path.exists(img) for img in image_files):
            print("\n您可以使用图像查看器打开这些PNG文件查看结果")
    
    else:
        print("无效选择")
    
    print("\n" + "=" * 50)
    print("使用说明:")
    print("- MATLAB版本: 运行 data.m 或 demo_distance_simulation.m")
    print("- Python版本: 运行 distance_simulation.py")
    print("- 数据分析: 运行 analyze_iq_data.py")
    print("- 详细说明: 查看 README.md")
    print("=" * 50)

if __name__ == "__main__":
    main()
