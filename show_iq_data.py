#!/usr/bin/env python3
"""
展示IQ数据的具体内容和结构
"""

import numpy as np
import matplotlib.pyplot as plt

def show_iq_data():
    """展示IQ数据的具体内容"""
    
    # 加载数据
    try:
        data = np.load('multi_station_data.npz')
    except FileNotFoundError:
        print("请先运行 multi_station_simulation.py 生成数据")
        return
    
    I_data = data['I_data']
    Q_data = data['Q_data']
    received_signals = data['received_signals']
    station_coords = data['station_coords']
    source_coords = data['source_coords']
    distances = data['distances']
    
    print("=" * 60)
    print("IQ数据内容展示")
    print("=" * 60)
    
    # 1. 数据结构信息
    print(f"\n1. 数据结构信息:")
    print(f"   I_data 形状: {I_data.shape} (基站数 x 采样点数)")
    print(f"   Q_data 形状: {Q_data.shape}")
    print(f"   复数信号形状: {received_signals.shape}")
    print(f"   数据类型: {I_data.dtype} (I/Q), {received_signals.dtype} (复数)")
    
    # 2. 基站信息
    print(f"\n2. 基站配置信息:")
    for i in range(len(station_coords)):
        print(f"   基站{i+1}: 坐标({station_coords[i,0]:.0f}, {station_coords[i,1]:.0f})m, "
              f"距离{distances[i]:.1f}m")
    
    # 3. 展示基站1的前20个IQ样本
    print(f"\n3. 基站1的前20个IQ样本:")
    print(f"   样本编号 |    I路数据    |    Q路数据    |   复数形式   |    幅度    |   相位(度)")
    print(f"   ---------|---------------|---------------|--------------|------------|----------")
    
    for i in range(20):
        I_val = I_data[0, i]
        Q_val = Q_data[0, i]
        complex_val = received_signals[0, i]
        magnitude = abs(complex_val)
        phase_deg = np.angle(complex_val) * 180 / np.pi
        
        print(f"   {i+1:8d} | {I_val:12.6f} | {Q_val:12.6f} | "
              f"{complex_val.real:6.3f}{complex_val.imag:+6.3f}j | "
              f"{magnitude:9.6f} | {phase_deg:8.2f}")
    
    # 4. 各基站功率统计
    print(f"\n4. 各基站信号统计:")
    print(f"   基站 |  平均功率(dB) |  最大幅度  |  最小幅度  |  标准差")
    print(f"   -----|---------------|------------|------------|----------")
    
    for i in range(len(station_coords)):
        signal = received_signals[i, :]
        power_db = 10 * np.log10(np.mean(np.abs(signal)**2))
        max_amp = np.max(np.abs(signal))
        min_amp = np.min(np.abs(signal))
        std_amp = np.std(np.abs(signal))
        
        print(f"   {i+1:4d} | {power_db:12.2f} | {max_amp:9.6f} | {min_amp:9.6f} | {std_amp:8.6f}")
    
    # 5. 展示不同基站的IQ数据差异
    print(f"\n5. 不同基站IQ数据对比 (第1个样本):")
    print(f"   基站 |    I路值     |    Q路值     |    幅度     |   相位(度)")
    print(f"   -----|--------------|--------------|-------------|----------")
    
    for i in range(len(station_coords)):
        I_val = I_data[i, 0]
        Q_val = Q_data[i, 0]
        magnitude = abs(received_signals[i, 0])
        phase_deg = np.angle(received_signals[i, 0]) * 180 / np.pi
        
        print(f"   {i+1:4d} | {I_val:11.6f} | {Q_val:11.6f} | {magnitude:10.6f} | {phase_deg:8.2f}")
    
    # 6. 数据访问示例
    print(f"\n6. 数据访问示例:")
    print(f"   # 访问基站1的所有I路数据")
    print(f"   station1_I = I_data[0, :]  # 形状: (1000,)")
    print(f"   ")
    print(f"   # 访问基站2的所有Q路数据")
    print(f"   station2_Q = Q_data[1, :]  # 形状: (1000,)")
    print(f"   ")
    print(f"   # 访问基站3的复数信号")
    print(f"   station3_complex = received_signals[2, :]  # 形状: (1000,)")
    print(f"   ")
    print(f"   # 计算基站4的信号功率")
    print(f"   station4_power = np.mean(np.abs(received_signals[3, :])**2)")
    
    # 7. 距离信息在IQ数据中的体现
    print(f"\n7. 距离信息在IQ数据中的体现:")
    print(f"   距离主要通过以下方式体现在IQ数据中:")
    print(f"   a) 信号幅度: 距离越远，信号越弱")
    print(f"   b) 相位延迟: 距离越远，相位延迟越大")
    print(f"   c) 路径损耗: 按照20*log10(4*π*d/λ)规律衰减")
    
    # 计算相位延迟验证
    print(f"\n   相位延迟验证:")
    fc = float(data['fc'])
    c = 3e8
    
    for i in range(len(station_coords)):
        theoretical_delay = -2 * np.pi * fc * distances[i] / c
        theoretical_delay_deg = theoretical_delay * 180 / np.pi
        
        # 计算平均相位（简化）
        avg_phase = np.mean(np.angle(received_signals[i, :]))
        avg_phase_deg = avg_phase * 180 / np.pi
        
        print(f"   基站{i+1}: 理论相位延迟={theoretical_delay_deg:.1f}°, "
              f"平均相位={avg_phase_deg:.1f}°")
    
    # 8. 数据质量指标
    print(f"\n8. 数据质量指标:")
    for i in range(len(station_coords)):
        signal = received_signals[i, :]
        
        # 信噪比估计
        signal_power = np.mean(np.abs(signal)**2)
        noise_estimate = np.var(np.diff(signal))  # 简化的噪声估计
        snr_estimate = 10 * np.log10(signal_power / noise_estimate)
        
        # PAPR计算
        peak_power = np.max(np.abs(signal)**2)
        avg_power = np.mean(np.abs(signal)**2)
        papr = 10 * np.log10(peak_power / avg_power)
        
        print(f"   基站{i+1}: SNR≈{snr_estimate:.1f}dB, PAPR={papr:.1f}dB")
    
    print(f"\n" + "=" * 60)
    print(f"数据展示完成！")
    print(f"=" * 60)

if __name__ == "__main__":
    show_iq_data()
