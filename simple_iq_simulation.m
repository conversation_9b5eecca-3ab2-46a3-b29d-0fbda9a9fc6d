% 简化的距离感知IQ数据仿真
% 不依赖任何特殊工具箱，只使用基础MATLAB函数

clear all; close all; clc;

fprintf('=== 简化距离感知IQ数据仿真 ===\n\n');

% 基础参数设置
derad = pi/180;                                    % 角度→弧度
radeg = 180/pi;                                    % 弧度→角度
twpi = 2*pi;
kelm = 8;                                          % 阵元数
dd = 0.5;                                          % 阵元间距(波长为单位)
d = 0:dd:(kelm-1)*dd;                             % 阵元位置
iwave = 3;                                         % 信源数
theta = [10 30 60];                               % 波达方向(度)
snr = 10;                                         % 信噪比(dB)
n = 500;                                          % 采样数

% 距离相关参数
c = 3e8;                                          % 光速 m/s
fc = 2.4e9;                                       % 载频 2.4GHz
lambda = c/fc;                                    % 波长
fs = 10e6;                                        % 采样频率 10MHz

% 辐射源到基站的距离(米)
distances = [1000, 2000, 5000];                  % 对应三个信源的距离

% 显示仿真参数
fprintf('仿真参数:\n');
fprintf('阵元数: %d\n', kelm);
fprintf('信源数: %d\n', iwave);
fprintf('载频: %.2f GHz\n', fc/1e9);
fprintf('采样频率: %.2f MHz\n', fs/1e6);
fprintf('信噪比: %d dB\n', snr);
fprintf('波长: %.3f 米\n', lambda);

% 基础信号生成
A = exp(-j*twpi*d.'*sin(theta*derad));           % 方向向量
S = randn(iwave, n) + j*randn(iwave, n);         % 基础信源信号

% 添加距离相关的相位和幅度衰减
S_with_distance = zeros(size(S));
for i = 1:iwave
    % 传播时延
    tau = distances(i) / c;                       % 传播时延
    
    % 路径损耗 (自由空间传播损耗)
    path_loss_db = 20*log10(4*pi*distances(i)/lambda);
    path_loss_linear = 10^(-path_loss_db/20);
    
    % 相位延迟 (由于传播距离)
    phase_delay = -2*pi*fc*tau;
    
    % 生成带有距离信息的信号
    t = (0:n-1)/fs;                              % 时间向量
    carrier = exp(j*(2*pi*fc*t + phase_delay));  % 载波信号
    
    % 调制基带信号到载频
    S_with_distance(i,:) = S(i,:) .* carrier * path_loss_linear;
end

% 接收信号
X = A * S_with_distance;                         % 接收信号

% 添加噪声 (不使用Communications Toolbox)
signal_power = mean(mean(abs(X).^2));            % 计算信号功率
noise_power = signal_power / (10^(snr/10));      % 根据SNR计算噪声功率
noise = sqrt(noise_power/2) * (randn(size(X)) + j*randn(size(X))); % 生成复高斯噪声
X1 = X + noise;                                  % 添加噪声

% 计算协方差矩阵
Rxx = X1 * X1' / n;                              % 协方差矩阵

% IQ数据分离
I_data = real(X1);                               % I路数据
Q_data = imag(X1);                               % Q路数据

% 显示信源信息
fprintf('\n信源信息:\n');
for i = 1:iwave
    path_loss = 20*log10(4*pi*distances(i)/lambda);
    fprintf('信源%d: 角度=%d°, 距离=%d米, 路径损耗=%.1fdB\n', i, theta(i), distances(i), path_loss);
end

% 绘制结果
figure(1);
subplot(2,2,1);
plot(real(X1(1,:)));
title('Element 1 I-channel Signal');
xlabel('Sample Index');
ylabel('Amplitude');
grid on;

subplot(2,2,2);
plot(imag(X1(1,:)));
title('Element 1 Q-channel Signal');
xlabel('Sample Index');
ylabel('Amplitude');
grid on;

subplot(2,2,3);
plot(abs(X1(1,:)));
title('Element 1 Signal Magnitude');
xlabel('Sample Index');
ylabel('Amplitude');
grid on;

subplot(2,2,4);
plot(angle(X1(1,:))*radeg);
title('Element 1 Signal Phase');
xlabel('Sample Index');
ylabel('Phase (degrees)');
grid on;

% 距离估计验证
figure(2);
% 计算不同距离下的路径损耗
test_distances = 500:100:6000;
path_losses = 20*log10(4*pi*test_distances/lambda);
plot(test_distances, path_losses, 'b-', 'LineWidth', 2);
title('Free Space Path Loss vs Distance');
xlabel('Distance (m)');
ylabel('Path Loss (dB)');
grid on;

% 标记仿真中使用的距离点
hold on;
for i = 1:iwave
    sim_loss = 20*log10(4*pi*distances(i)/lambda);
    plot(distances(i), sim_loss, 'ro', 'MarkerSize', 8, 'LineWidth', 2);
    text(distances(i), sim_loss+5, sprintf('Source%d', i));
end
hold off;

% 简化的距离估计分析
fprintf('\n=== 距离估计分析 ===\n');

% 基于功率的距离估计
fprintf('\n基于接收功率的距离估计:\n');
for i = 1:kelm
    % 计算接收功率
    received_power = mean(abs(X1(i,:)).^2);
    received_power_db = 10*log10(received_power);
    fprintf('阵元%d接收功率: %.2f dB\n', i, received_power_db);
end

% 各阵元功率对比
figure(3);
powers = zeros(1, kelm);
for i = 1:kelm
    powers(i) = 10*log10(mean(abs(X1(i,:)).^2));
end
plot(1:kelm, powers, 'o-', 'LineWidth', 2, 'MarkerSize', 8);
title('Average Received Power per Element');
xlabel('Element Number');
ylabel('Power (dB)');
grid on;

% IQ星座图
figure(4);
for i = 1:min(4, kelm)
    subplot(2,2,i);
    samples = X1(i, 1:200);  % 前200个样本
    plot(real(samples), imag(samples), '.', 'MarkerSize', 4);
    title(sprintf('Element %d IQ Constellation', i));
    xlabel('I');
    ylabel('Q');
    grid on;
    axis equal;
end

% 保存数据到工作空间
fprintf('\n=== 仿真完成 ===\n');
fprintf('生成的数据变量:\n');
fprintf('I_data: %dx%d 矩阵 (I路数据)\n', size(I_data));
fprintf('Q_data: %dx%d 矩阵 (Q路数据)\n', size(Q_data));
fprintf('X1: %dx%d 复数矩阵 (完整IQ数据)\n', size(X1));
fprintf('Rxx: %dx%d 协方差矩阵\n', size(Rxx));
fprintf('distances: 信源距离 [%d %d %d] 米\n', distances);
fprintf('theta: 信源角度 [%d %d %d] 度\n', theta);

fprintf('\n数据已保存到工作空间，可用于进一步分析。\n');
