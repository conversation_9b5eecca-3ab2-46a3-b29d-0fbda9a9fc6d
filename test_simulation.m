% 测试脚本 - 验证仿真是否正常运行
% 只使用基础MATLAB函数

clear all; close all; clc;

fprintf('=== 测试距离感知IQ数据仿真 ===\n');

try
    % 运行简化仿真
    run('simple_iq_simulation.m');
    
    fprintf('\n✓ 仿真成功完成！\n');
    fprintf('✓ 生成了IQ数据\n');
    fprintf('✓ 创建了可视化图表\n');
    
    % 验证数据
    if exist('I_data', 'var') && exist('Q_data', 'var')
        fprintf('✓ I/Q数据已生成，大小: %dx%d\n', size(I_data));
    else
        fprintf('✗ I/Q数据生成失败\n');
    end
    
    if exist('X1', 'var')
        fprintf('✓ 复数IQ数据已生成\n');
    else
        fprintf('✗ 复数IQ数据生成失败\n');
    end
    
    if exist('distances', 'var')
        fprintf('✓ 距离信息: [%d %d %d] 米\n', distances);
    else
        fprintf('✗ 距离信息缺失\n');
    end
    
catch ME
    fprintf('✗ 仿真失败: %s\n', ME.message);
    fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
end

fprintf('\n=== 测试完成 ===\n');
