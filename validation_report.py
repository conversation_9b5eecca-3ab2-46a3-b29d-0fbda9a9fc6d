#!/usr/bin/env python3
"""
IQ数据质量验证报告
全面评估生成的IQ数据是否满足定位需求
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import minimize
import warnings
warnings.filterwarnings('ignore')

def load_simulation_data():
    """加载仿真数据"""
    try:
        data = np.load('multi_station_data.npz')
        return data
    except FileNotFoundError:
        print("错误：未找到仿真数据文件 multi_station_data.npz")
        return None

def improved_rssi_localization(station_coords, received_powers, fc, reference_power):
    """改进的RSSI定位算法"""
    c = 3e8
    lambda_wave = c / fc
    
    # 从接收功率计算距离
    estimated_distances = []
    for power in received_powers:
        path_loss_db = reference_power - power
        if path_loss_db > 0:
            distance = lambda_wave * 10**(path_loss_db/20) / (4*np.pi)
        else:
            distance = 10.0  # 最小距离
        estimated_distances.append(distance)
    
    estimated_distances = np.array(estimated_distances)
    
    def objective_function(pos):
        x, y = pos
        total_error = 0
        for i, (sx, sy) in enumerate(station_coords):
            theoretical_distance = np.sqrt((x - sx)**2 + (y - sy)**2)
            if estimated_distances[i] > 0:
                error = ((theoretical_distance - estimated_distances[i]) / estimated_distances[i])**2
            else:
                error = (theoretical_distance - estimated_distances[i])**2
            total_error += error
        return total_error
    
    # 多个初始点优化
    initial_guesses = [
        np.mean(station_coords, axis=0),
        [0, 0], [500, 500], [-500, -500]
    ]
    
    best_result = None
    best_error = float('inf')
    
    for guess in initial_guesses:
        try:
            result = minimize(objective_function, guess, method='BFGS')
            if result.success and result.fun < best_error:
                best_result = result
                best_error = result.fun
        except:
            continue
    
    return best_result.x if best_result else np.mean(station_coords, axis=0)

def calculate_signal_quality_metrics(received_signals):
    """计算信号质量指标"""
    metrics = {}
    
    for i, signal in enumerate(received_signals):
        # 信号功率
        signal_power = np.mean(np.abs(signal)**2)
        signal_power_db = 10 * np.log10(signal_power)
        
        # 峰值平均功率比 (PAPR)
        peak_power = np.max(np.abs(signal)**2)
        papr_db = 10 * np.log10(peak_power / signal_power)
        
        # 信号动态范围
        min_power = np.min(np.abs(signal)**2)
        dynamic_range_db = 10 * np.log10(peak_power / min_power)
        
        # 相位稳定性 (相位标准差)
        phases = np.angle(signal)
        phase_unwrapped = np.unwrap(phases)
        phase_stability = np.std(np.diff(phase_unwrapped))
        
        metrics[f'station_{i+1}'] = {
            'signal_power_db': signal_power_db,
            'papr_db': papr_db,
            'dynamic_range_db': dynamic_range_db,
            'phase_stability': phase_stability
        }
    
    return metrics

def evaluate_distance_estimation_accuracy(station_coords, source_coords, received_powers, fc, reference_power):
    """评估距离估计精度"""
    c = 3e8
    lambda_wave = c / fc
    
    # 计算真实距离
    true_distances = []
    for coord in station_coords:
        distance = np.sqrt((coord[0] - source_coords[0])**2 + (coord[1] - source_coords[1])**2)
        true_distances.append(distance)
    
    # 基于功率估计距离
    estimated_distances = []
    for power in received_powers:
        path_loss_db = reference_power - power
        if path_loss_db > 0:
            distance = lambda_wave * 10**(path_loss_db/20) / (4*np.pi)
        else:
            distance = 10.0
        estimated_distances.append(distance)
    
    # 计算误差统计
    errors = []
    relative_errors = []
    for true_dist, est_dist in zip(true_distances, estimated_distances):
        error = abs(est_dist - true_dist)
        relative_error = error / true_dist * 100
        errors.append(error)
        relative_errors.append(relative_error)
    
    return {
        'true_distances': true_distances,
        'estimated_distances': estimated_distances,
        'absolute_errors': errors,
        'relative_errors': relative_errors,
        'mean_absolute_error': np.mean(errors),
        'max_absolute_error': np.max(errors),
        'mean_relative_error': np.mean(relative_errors),
        'max_relative_error': np.max(relative_errors)
    }

def main():
    print("=" * 60)
    print("IQ数据质量验证报告")
    print("=" * 60)
    
    # 加载数据
    data = load_simulation_data()
    if data is None:
        return
    
    received_signals = data['received_signals']
    station_coords = data['station_coords']
    source_coords = data['source_coords']
    distances = data['distances']
    fc = float(data['fc'])
    
    print(f"\n1. 仿真场景信息")
    print(f"   辐射源位置: ({source_coords[0]:.1f}, {source_coords[1]:.1f}) 米")
    print(f"   载频: {fc/1e9:.2f} GHz")
    print(f"   基站数量: {len(station_coords)}")
    
    # 计算接收功率
    received_powers = []
    for signal in received_signals:
        power_db = 10 * np.log10(np.mean(np.abs(signal)**2))
        received_powers.append(power_db)
    
    print(f"\n2. 接收功率分析")
    for i, power in enumerate(received_powers):
        print(f"   基站{i+1}: {power:.2f} dB (距离: {distances[i]:.1f}m)")
    
    # 估计发射功率
    min_distance_idx = np.argmin(distances)
    min_distance = distances[min_distance_idx]
    min_power = received_powers[min_distance_idx]
    c = 3e8
    lambda_wave = c / fc
    theoretical_loss = 20 * np.log10(4 * np.pi * min_distance / lambda_wave)
    estimated_tx_power = min_power + theoretical_loss
    
    print(f"\n3. 发射功率估计")
    print(f"   估计发射功率: {estimated_tx_power:.2f} dBm")
    print(f"   基于最近基站 (基站{min_distance_idx+1}, 距离{min_distance:.1f}m)")
    
    # RSSI定位验证
    print(f"\n4. RSSI定位验证")
    rssi_position = improved_rssi_localization(station_coords, received_powers, fc, estimated_tx_power)
    rssi_error = np.sqrt((rssi_position[0] - source_coords[0])**2 + (rssi_position[1] - source_coords[1])**2)
    
    print(f"   估计位置: ({rssi_position[0]:.1f}, {rssi_position[1]:.1f}) 米")
    print(f"   定位误差: {rssi_error:.1f} 米")
    print(f"   相对误差: {rssi_error/np.mean(distances)*100:.2f}%")
    
    # 距离估计精度评估
    print(f"\n5. 距离估计精度评估")
    distance_eval = evaluate_distance_estimation_accuracy(
        station_coords, source_coords, received_powers, fc, estimated_tx_power
    )
    
    print(f"   平均绝对误差: {distance_eval['mean_absolute_error']:.1f} 米")
    print(f"   最大绝对误差: {distance_eval['max_absolute_error']:.1f} 米")
    print(f"   平均相对误差: {distance_eval['mean_relative_error']:.2f}%")
    print(f"   最大相对误差: {distance_eval['max_relative_error']:.2f}%")
    
    # 信号质量指标
    print(f"\n6. 信号质量指标")
    quality_metrics = calculate_signal_quality_metrics(received_signals)
    
    for station, metrics in quality_metrics.items():
        print(f"   {station}:")
        print(f"     信号功率: {metrics['signal_power_db']:.2f} dB")
        print(f"     PAPR: {metrics['papr_db']:.2f} dB")
        print(f"     动态范围: {metrics['dynamic_range_db']:.2f} dB")
        print(f"     相位稳定性: {metrics['phase_stability']:.4f} rad")
    
    # 数据质量评级
    print(f"\n7. 数据质量评级")
    
    # 定位精度评级
    if rssi_error < 50:
        location_grade = "优秀"
    elif rssi_error < 100:
        location_grade = "良好"
    elif rssi_error < 200:
        location_grade = "一般"
    else:
        location_grade = "较差"
    
    # 距离估计精度评级
    if distance_eval['mean_relative_error'] < 1:
        distance_grade = "优秀"
    elif distance_eval['mean_relative_error'] < 5:
        distance_grade = "良好"
    elif distance_eval['mean_relative_error'] < 10:
        distance_grade = "一般"
    else:
        distance_grade = "较差"
    
    # 信号质量评级
    avg_papr = np.mean([m['papr_db'] for m in quality_metrics.values()])
    if avg_papr < 10:
        signal_grade = "优秀"
    elif avg_papr < 15:
        signal_grade = "良好"
    else:
        signal_grade = "一般"
    
    print(f"   定位精度: {location_grade} (误差: {rssi_error:.1f}m)")
    print(f"   距离估计: {distance_grade} (相对误差: {distance_eval['mean_relative_error']:.2f}%)")
    print(f"   信号质量: {signal_grade} (平均PAPR: {avg_papr:.2f}dB)")
    
    # 总体评估
    print(f"\n8. 总体评估")
    
    if rssi_error < 100 and distance_eval['mean_relative_error'] < 5:
        overall_grade = "优秀"
        recommendation = "IQ数据质量优秀，完全满足定位需求"
    elif rssi_error < 200 and distance_eval['mean_relative_error'] < 10:
        overall_grade = "良好"
        recommendation = "IQ数据质量良好，基本满足定位需求"
    else:
        overall_grade = "需要改进"
        recommendation = "建议调整仿真参数以提高数据质量"
    
    print(f"   总体评级: {overall_grade}")
    print(f"   建议: {recommendation}")
    
    # 生成可视化报告
    plt.figure(figsize=(15, 10))
    
    # 子图1: 场景布局和定位结果
    plt.subplot(2, 3, 1)
    plt.plot(source_coords[0], source_coords[1], 'r*', markersize=15, label='真实位置')
    plt.plot(rssi_position[0], rssi_position[1], 'go', markersize=12, label=f'RSSI定位 (误差:{rssi_error:.1f}m)')
    
    for i, coord in enumerate(station_coords):
        plt.plot(coord[0], coord[1], 'bs', markersize=8)
        plt.text(coord[0]+50, coord[1]+50, f'S{i+1}', fontsize=8)
    
    plt.xlabel('X (m)')
    plt.ylabel('Y (m)')
    plt.title('定位结果')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.axis('equal')
    
    # 子图2: 接收功率对比
    plt.subplot(2, 3, 2)
    plt.bar(range(1, len(received_powers)+1), received_powers)
    plt.xlabel('基站编号')
    plt.ylabel('接收功率 (dB)')
    plt.title('各基站接收功率')
    plt.grid(True, alpha=0.3)
    
    # 子图3: 距离估计精度
    plt.subplot(2, 3, 3)
    x_pos = range(1, len(distances)+1)
    plt.plot(x_pos, distance_eval['true_distances'], 'bo-', label='真实距离')
    plt.plot(x_pos, distance_eval['estimated_distances'], 'rs-', label='估计距离')
    plt.xlabel('基站编号')
    plt.ylabel('距离 (m)')
    plt.title('距离估计精度')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 子图4: 信号幅度
    plt.subplot(2, 3, 4)
    for i in range(min(2, len(received_signals))):
        plt.plot(np.abs(received_signals[i, :100]), label=f'基站{i+1}')
    plt.xlabel('样本')
    plt.ylabel('幅度')
    plt.title('信号幅度 (前100样本)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 子图5: IQ星座图
    plt.subplot(2, 3, 5)
    signal = received_signals[0, :200]  # 基站1的前200个样本
    plt.plot(np.real(signal), np.imag(signal), '.', markersize=2)
    plt.xlabel('I')
    plt.ylabel('Q')
    plt.title('基站1 IQ星座图')
    plt.grid(True, alpha=0.3)
    plt.axis('equal')
    
    # 子图6: 误差分析
    plt.subplot(2, 3, 6)
    plt.bar(range(1, len(distance_eval['relative_errors'])+1), distance_eval['relative_errors'])
    plt.xlabel('基站编号')
    plt.ylabel('相对误差 (%)')
    plt.title('距离估计相对误差')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('validation_report.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print(f"\n验证报告已生成: validation_report.png")
    print("=" * 60)

if __name__ == "__main__":
    main()
